import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from the root .env file
const envPath = path.join(__dirname, '..', '.env');
dotenv.config({ path: envPath });

// Create transporter for sending emails
const createTransporter = () => {
  // For production with Cloudflare email
  if (process.env.NODE_ENV === 'production') {
    return nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'smtp.gmail.com', // You'll need to configure this for your domain
      port: process.env.SMTP_PORT || 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER, // Your domain email
        pass: process.env.SMTP_PASS, // Your email password or app password
      },
    });
  } else {
    // For development - use a test account or console logging
    console.log('Email service running in development mode - emails will be logged to console');
    return null;
  }
};

// Generate a 6-digit verification code
export const generateVerificationCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send verification email
export const sendVerificationEmail = async (email, verificationCode, fullName) => {
  try {
    const transporter = createTransporter();
    
    // In development, just log the email
    if (!transporter) {
      console.log('\n=== EMAIL VERIFICATION (DEVELOPMENT MODE) ===');
      console.log(`To: ${email}`);
      console.log(`Name: ${fullName}`);
      console.log(`Verification Code: ${verificationCode}`);
      console.log('===============================================\n');
      return { success: true, messageId: 'dev-mode' };
    }

    const mailOptions = {
      from: `"SocialSpark AI" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
      to: email,
      subject: 'Verify Your SocialSpark AI Account',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify Your Account</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #6366f1; }
            .verification-code { 
              background: #f8fafc; 
              border: 2px solid #e2e8f0; 
              border-radius: 8px; 
              padding: 20px; 
              text-align: center; 
              margin: 20px 0; 
            }
            .code { 
              font-size: 32px; 
              font-weight: bold; 
              color: #6366f1; 
              letter-spacing: 4px; 
              margin: 10px 0; 
            }
            .footer { 
              margin-top: 30px; 
              padding-top: 20px; 
              border-top: 1px solid #e2e8f0; 
              font-size: 14px; 
              color: #64748b; 
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">SocialSpark AI</div>
            </div>
            
            <h1>Welcome to SocialSpark AI, ${fullName}!</h1>
            
            <p>Thank you for signing up! To complete your account creation, please verify your email address using the code below:</p>
            
            <div class="verification-code">
              <p><strong>Your verification code is:</strong></p>
              <div class="code">${verificationCode}</div>
              <p><small>This code will expire in 15 minutes</small></p>
            </div>
            
            <p>Enter this code on the verification page to activate your account and start creating amazing social media content with AI!</p>
            
            <p>If you didn't create an account with SocialSpark AI, please ignore this email.</p>
            
            <div class="footer">
              <p>Best regards,<br>The SocialSpark AI Team</p>
              <p><small>This is an automated email. Please do not reply to this message.</small></p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        Welcome to SocialSpark AI, ${fullName}!
        
        Thank you for signing up! To complete your account creation, please verify your email address using the code below:
        
        Your verification code is: ${verificationCode}
        
        This code will expire in 15 minutes.
        
        Enter this code on the verification page to activate your account and start creating amazing social media content with AI!
        
        If you didn't create an account with SocialSpark AI, please ignore this email.
        
        Best regards,
        The SocialSpark AI Team
      `
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Verification email sent:', info.messageId);
    
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Error sending verification email:', error);
    return { success: false, error: error.message };
  }
};

// Clean up expired verification codes (run this periodically)
export const cleanupExpiredCodes = async (dbConnection) => {
  try {
    if (process.env.NODE_ENV === 'production') {
      // MySQL cleanup
      await dbConnection.query(
        'DELETE FROM email_verifications WHERE expires_at < NOW() OR verified = TRUE'
      );
    } else {
      // SQLite cleanup
      const db = dbConnection;
      db.prepare(
        'DELETE FROM email_verifications WHERE expires_at < datetime("now") OR verified = 1'
      ).run();
    }
    console.log('Cleaned up expired verification codes');
  } catch (error) {
    console.error('Error cleaning up expired codes:', error);
  }
};
