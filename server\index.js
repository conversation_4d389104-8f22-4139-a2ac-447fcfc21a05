import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { GoogleGenAI, Modality } from '@google/genai';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { initializeDatabase } from './database.js';
import { testConnection, getOnlineUsersCount, trackAnalyticsEvent, trackUserSession, updateUserActivity, removeUserSession, getOne, getAll, getCurrentTimestamp } from './db-operations.js';
import {
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  verifyToken,
  hasUnlimitedPosts,
  getRateLimit,
  savePost
} from './auth.js';
import { verifyPayPalPayment, getPlanDetails, getNextBillingDate, getSubscriptionPlanDetails, getAccessToken as getPayPalAccessToken, verifyWebhookSignature } from './paypal.js';
import { handleWebhookEvent } from './webhook-handlers.js';
import { runSubscriptionMaintenance } from './subscription-sync.js';
import { authenticateAdmin, checkAdminStatus } from './admin-middleware.js';
import { createEmailVerification, verifyEmailAndCreateAccount, resendVerificationCode } from './email-verification.js';
import {
  validateBody,
  validateParams,
  registerSchema,
  loginSchema,
  verifyEmailSchema,
  resendVerificationSchema,
  generatePostSchema,
  generateImageSchema,
  updateProfileSchema,
  contactSchema,
  sanitizeObject,
  uuidSchema
} from './validation.js';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create images directory if it doesn't exist
const imagesDir = path.join(__dirname, '..', 'data', 'images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
  console.log('Created images directory at:', imagesDir);
}

// Load environment variables
dotenv.config();

// Function to reload environment variables
const reloadEnvVars = () => {
  try {
    const envPath = path.join(__dirname, '..', '.env');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = dotenv.parse(envContent);

    // Update process.env with the new values
    Object.keys(envVars).forEach(key => {
      process.env[key] = envVars[key];
    });

    console.log('Environment variables reloaded successfully');
  } catch (error) {
    console.error('Error reloading environment variables:', error);
  }
};

// Reload environment variables to ensure they're up to date
reloadEnvVars();


// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;



// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
    },
  },
  crossOriginEmbedderPolicy: false // Allow embedding for PayPal
}));

app.use(cors());

// Increase JSON payload size limit to 50MB to handle large base64 images
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));

// Sanitization middleware
app.use((req, res, next) => {
  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  next();
});





// Authentication middleware
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Authentication token required' });
  }

  const user = verifyToken(token);
  if (!user) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }

  // Update user activity
  try {
    await updateUserActivity(token);
  } catch (activityError) {
    console.error('Error updating user activity:', activityError);
    // Don't fail the request if activity update fails
  }

  req.user = user;
  next();
};

// Optional authentication middleware for contact form
const optionalAuth = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token) {
    const user = verifyToken(token);
    if (user) {
      req.user = user;
    }
  }

  next();
};

// Get all available API keys from environment variables
const getAvailableApiKeys = () => {
  const keys = [];

  // Add main API key
  if (process.env.GOOGLE_API_KEY) {
    keys.push(process.env.GOOGLE_API_KEY);
  }

  // Add fallback keys (GOOGLE_API_KEY_1, GOOGLE_API_KEY_2, etc.)
  let i = 1;
  while (process.env[`GOOGLE_API_KEY_${i}`]) {
    keys.push(process.env[`GOOGLE_API_KEY_${i}`]);
    i++;
  }

  return keys;
};

// Initialize Google GenAI with fallback support
const getAiModel = (quality = 'balanced', apiKeyIndex = 0) => {
  const availableKeys = getAvailableApiKeys();

  if (availableKeys.length === 0) {
    throw new Error('No Google API keys found in environment variables');
  }

  if (apiKeyIndex >= availableKeys.length) {
    throw new Error('All available API keys have been exhausted');
  }

  const apiKey = availableKeys[apiKeyIndex];
  const ai = new GoogleGenAI({ apiKey });

  // Select model based on quality parameter
  let modelName;
  switch (quality.toLowerCase()) {
    case 'quality':
      modelName = 'gemini-2.5-flash-preview-04-17';
      break;
    case 'fast':
      modelName = 'gemini-2.0-flash-lite';
      break;
    case 'balanced':
    default:
      modelName = 'gemma-3n-e4b-it';
      break;
  }

  return { ai, modelName, apiKeyIndex, totalKeys: availableKeys.length };
};

// Helper function to attempt API call with fallback
const attemptApiCallWithFallback = async (apiCall, quality = 'balanced') => {
  const availableKeys = getAvailableApiKeys();
  let lastError = null;

  for (let i = 0; i < availableKeys.length; i++) {
    try {
      console.log(`Attempting API call with key ${i + 1}/${availableKeys.length}`);
      const { ai, modelName } = getAiModel(quality, i);
      const result = await apiCall(ai, modelName);
      console.log(`API call successful with key ${i + 1}`);
      return result;
    } catch (error) {
      console.error(`API call failed with key ${i + 1}:`, error.message);
      lastError = error;

      // Check if this is an API key related error that should trigger fallback
      const errorString = error.message || error.toString();
      const isApiKeyError = errorString.includes('API_KEY_INVALID') ||
                           errorString.includes('PERMISSION_DENIED') ||
                           errorString.includes('RESOURCE_EXHAUSTED') ||
                           errorString.includes('QUOTA_EXCEEDED') ||
                           errorString.includes('INVALID_ARGUMENT') ||
                           errorString.includes('API key expired') ||
                           errorString.includes('API key invalid') ||
                           errorString.includes('quota exceeded');

      if (!isApiKeyError && i === 0) {
        // If it's not an API key error, don't try other keys
        throw error;
      }
    }
  }

  // All keys failed
  throw new Error(`All API keys failed. Last error: ${lastError?.message || 'Unknown error'}`);
};

// API Routes
// Generate post content API endpoint
app.post('/api/generate-post', validateBody(generatePostSchema), async (req, res) => {
  try {
    // Get user from token if available
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    let userId = null;
    let profile = null;
    let hasUnlimitedAccess = false;

    if (token) {
      const user = verifyToken(token);
      if (user) {
        userId = user.sub;
        const profileResult = await getUserProfile(userId);
        if (!profileResult.error) {
          profile = profileResult.data;

          // Check if user has unlimited posts
          hasUnlimitedAccess = await hasUnlimitedPosts(userId);

          // Check if user has reached post limit (if they don't have unlimited access)
          if (!hasUnlimitedAccess && profile.posts_count >= profile.posts_limit) {
            return res.status(403).json({
              error: `You've reached your limit of ${profile.posts_limit} posts for your ${profile.plan} plan this month.`,
              limitReached: true
            });
          }

          // Check rate limiting using the new post-count based system
          const rateLimit = getRateLimit(profile.plan);

          // Query posts created in the last minute for this user
          const oneMinuteAgo = new Date(Date.now() - 60 * 1000).toISOString();
          const recentPosts = await getAll(`
            SELECT created_at
            FROM posts
            WHERE user_id = ?
            AND created_at >= ?
            ORDER BY created_at ASC
          `, [userId, oneMinuteAgo]);

          const postsInLastMinute = recentPosts.length;

          // Check if user has exceeded rate limit
          if (postsInLastMinute >= rateLimit) {
            // Find the oldest post that needs to expire for user to make another post
            // We need to wait until the oldest post is more than 60 seconds old
            const oldestPostTime = new Date(recentPosts[0].created_at); // First post since we ordered ASC
            const now = new Date();
            const ageOfOldestPost = (now - oldestPostTime) / 1000; // seconds

            // Calculate how much longer we need to wait for the oldest post to be 60+ seconds old
            const waitTimeSeconds = Math.ceil(60 - ageOfOldestPost);

            // If wait time is negative or zero, something is wrong with our calculation, default to 1
            const actualWaitTime = waitTimeSeconds > 0 ? waitTimeSeconds : 1;

            return res.status(429).json({
              error: `Rate limit exceeded. ${profile.plan.toUpperCase()} plan allows ${rateLimit} post${rateLimit > 1 ? 's' : ''} per minute. Please wait ${actualWaitTime} seconds.`,
              waitTimeLeft: actualWaitTime,
              postsInLastMinute,
              rateLimit,
              plan: profile.plan
            });
          }
        } else {
          return res.status(404).json({ error: 'User profile not found' });
        }
      } else {
        return res.status(401).json({ error: 'Invalid authentication token' });
      }
    } else {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const {
      platform = 'Instagram',
      tone = 'friendly',
      topic = 'spring gardening tips',
      hashtagCount = 5,
      imageSize = '4:5',
      quality = 'balanced',
      postId = null // Allow passing a post ID to update an existing post
    } = req.body;

    // Log if we're updating an existing post
    if (postId) {
      console.log(`Generating content for existing post ${postId}`);
    }

    // Check if FREE user is trying to use Quality mode
    if (profile && profile.plan === 'free' && quality.toLowerCase() === 'quality') {
      return res.status(403).json({
        error: 'Quality mode is not available on the FREE plan. Please upgrade to use this feature.',
        qualityRestriction: true
      });
    }

    // Convert "Auto" to a specific aspect ratio for image generation
    const actualImageSize = imageSize === "Auto" ? "1:1" : imageSize;

    // Build the dynamic prompt
    const userPrompt = `
Generate a social media post for ${platform} with a ${tone} tone about: "${topic}"

Your response should include:
1. A caption that follows ${platform}'s best practices and character limits
2. ${hashtagCount > 0 ? `${hashtagCount} relevant hashtags` : 'No hashtags'}
3. An image prompt that would work well for ${actualImageSize} aspect ratio

IMPORTANT: In the image prompt, explicitly include the dimensions/aspect ratio "${actualImageSize}" so the image generation AI knows the correct size to use.

Format your response as:
CAPTION: [The social media caption text]
HASHTAGS: [The hashtags, if requested]
IMAGE_PROMPT: [A detailed prompt for generating an image that matches the caption, including "${actualImageSize}" aspect ratio]
    `.trim();

    console.log('Generating post content with fallback API keys...');
    console.log('Prompt:', userPrompt);

    // Use fallback system for text generation
    const result = await attemptApiCallWithFallback(async (ai, modelName) => {
      console.log('Sending request to Gemini API with model:', modelName);
      return await ai.models.generateContent({
        model: modelName,
        contents: userPrompt,
      });
    }, quality);

    console.log('Received response from Gemini API');
    // In the new API, the response structure is different
    console.log('Response structure:', Object.keys(result));
    console.log('Full response:', JSON.stringify(result, null, 2));

    // Get the text from the response
    let text;
    if (result.candidates && result.candidates[0] && result.candidates[0].content) {
      // New API structure
      text = result.candidates[0].content.parts[0].text;
    } else if (result.response && typeof result.response.text === 'function') {
      // Old API structure
      text = result.response.text();
    } else if (typeof result.text === 'function') {
      // Another possible structure
      text = result.text();
    } else if (typeof result.text === 'string') {
      // Direct text property
      text = result.text;
    } else {
      // Fallback
      throw new Error('Unable to extract text from API response');
    }

    console.log('Raw response text:', text);

    // Parse the response into structured format
    const captionMatch = text.match(/CAPTION:(.*?)(?=HASHTAGS:|IMAGE_PROMPT:|$)/s);
    const hashtagsMatch = text.match(/HASHTAGS:(.*?)(?=IMAGE_PROMPT:|$)/s);
    const imagePromptMatch = text.match(/IMAGE_PROMPT:(.*?)(?=$)/s);

    console.log('Parsed matches:', {
      captionMatch: captionMatch ? 'found' : 'not found',
      hashtagsMatch: hashtagsMatch ? 'found' : 'not found',
      imagePromptMatch: imagePromptMatch ? 'found' : 'not found'
    });

    // Format the response to match the expected structure
    const structuredResponse = {
      CAPTION: captionMatch ? captionMatch[1].trim() : '',
      HASHTAGS: hashtagsMatch ? hashtagsMatch[1].trim() : '',
      IMAGE_PROMPT: imagePromptMatch ? imagePromptMatch[1].trim() : '',
    };

    console.log('Structured response:', structuredResponse);

    // Update last_post_time if we have a valid profile
    if (userId && profile) {
      try {
        await updateUserProfile(userId, {
          last_post_time: getCurrentTimestamp()
        });
        console.log('Updated last_post_time for user:', userId);
      } catch (err) {
        console.error('Error updating last_post_time:', err);
      }
    }

    res.json(structuredResponse);
  } catch (error) {
    console.error('Error generating post:', error);

    // If all API keys failed, return the user's original content with an error message
    if (error.message && error.message.includes('All API keys failed')) {
      return res.status(500).json({
        error: 'All API keys have failed or reached their limits. Please try again later.',
        originalContent: {
          platform: platform || 'Unknown',
          tone: tone || 'Unknown',
          topic: topic || 'Unknown',
          hashtagCount: hashtagCount || 0,
          imageSize: imageSize || 'Auto'
        },
        allKeysFailed: true
      });
    }

    res.status(500).json({ error: error.message || 'Failed to generate post' });
  }
});

// Image generation endpoint
app.post('/api/generate-image', validateBody(generateImageSchema), async (req, res) => {
  try {
    const { imagePrompt, postContent, caption, userId } = req.body;

    if (!imagePrompt) {
      return res.status(400).json({ error: 'Image prompt is required' });
    }

    // Check if user has reached post limit
    if (userId) {
      const user = verifyToken(userId);
      if (user) {
        const actualUserId = user.sub;

        // Check if user has unlimited posts
        const hasUnlimitedAccess = await hasUnlimitedPosts(actualUserId);

        if (!hasUnlimitedAccess) {
          // Check if user has reached post limit
          const profileResult = await getUserProfile(actualUserId);

          if (!profileResult.error) {
            const profile = profileResult.data;

            if (profile.posts_count >= profile.posts_limit) {
              return res.status(403).json({
                error: `You've reached your limit of ${profile.posts_limit} posts for your ${profile.plan} plan this month.`,
                limitReached: true
              });
            }
          }
        }
      }
    }

    console.log('Generating image with prompt:', imagePrompt);

    // Use fallback system for image generation
    const response = await attemptApiCallWithFallback(async (ai, modelName) => {
      console.log('Sending image generation request to Gemini API');
      return await ai.models.generateContent({
        model: "gemini-2.0-flash-preview-image-generation",
        contents: imagePrompt,
        config: {
          responseModalities: [Modality.IMAGE, Modality.TEXT],
        },
      });
    }, 'balanced'); // Use balanced quality for image generation

    console.log('Image generation response structure:', Object.keys(response));

    // Log the full response for debugging
    console.log('Full response structure:', JSON.stringify(response, null, 2));

    // Check if we have candidates
    if (!response.candidates || response.candidates.length === 0) {
      throw new Error('No candidates returned from the API');
    }

    // Check if we have content
    if (!response.candidates[0].content || !response.candidates[0].content.parts) {
      throw new Error('No content parts found in the response');
    }

    // Extract the image data
    const parts = response.candidates[0].content.parts;
    console.log('Content parts:', parts.map(p => p.inlineData ? 'inlineData' : p.text ? 'text' : 'unknown'));

    const part = parts.find(p => p.inlineData);
    if (!part || !part.inlineData) {
      throw new Error('No image data returned in the response');
    }

    const imageData = part.inlineData.data;
    const mimeType = part.inlineData.mimeType;

    // Generate a unique ID for the image
    const imageId = uuidv4();
    const fileExtension = mimeType.split('/')[1] || 'jpeg';
    const fileName = `${imageId}.${fileExtension}`;
    const filePath = path.join(imagesDir, fileName);

    // Extract postId from the request for logging
    const requestPostId = req.body.postId;
    console.log('Image generation request received with postId:', requestPostId || 'none');

    // Save the image to the filesystem
    try {
      fs.writeFileSync(filePath, Buffer.from(imageData, 'base64'));
      console.log(`Image saved to ${filePath}`);

      // If we have user ID and post content, automatically save the post
      let postResult = null;
      if (userId && postContent) {
        // Verify the token
        const user = verifyToken(userId);
        if (user) {
          const actualUserId = user.sub;

          // Parse the post content
          let parsedContent;
          try {
            parsedContent = JSON.parse(postContent);
          } catch (err) {
            console.error('Error parsing post content:', err);
            parsedContent = { error: 'Invalid JSON' };
          }

          // Check if we have a postId - if so, we'll update an existing post
          const postId = parsedContent.postId;

          // Import the prevent-duplicate-posts module
          const { preventDuplicatePost } = await import('./prevent-duplicate-posts.js');

          // Check if we should update an existing post
          const { shouldUpdate, postId: finalPostId } = await preventDuplicatePost(actualUserId, postId);

          if (finalPostId) {
            console.log(`Updating existing post ${finalPostId} with new image for user ${actualUserId}`);
            // Update the parsedContent with the final post ID
            parsedContent.postId = finalPostId;
          } else {
            console.log(`Creating new post for user ${actualUserId}`);
          }

          // Ensure caption is properly formatted
          let finalCaption = caption;

          // If caption is null or undefined, create an empty string
          if (!finalCaption) {
            finalCaption = '';
            console.log('No caption provided, using empty string');
          } else {
            console.log('Using provided caption:', finalCaption.substring(0, 100) + (finalCaption.length > 100 ? '...' : ''));
          }

          // Save or update the post with the image path
          const postIdToUse = finalPostId || postId;
          console.log(`Using post ID for saving: ${postIdToUse || 'none (creating new post)'}`);

          // If we're updating an existing post, preserve its existing caption data
          let finalContentToSave = postContent;
          let finalCaptionToSave = finalCaption;

          if (postIdToUse) {
            // Get the existing post to preserve its caption data
            const existingPost = await getOne('SELECT * FROM posts WHERE id = ? AND user_id = ?', [postIdToUse, actualUserId]);

            if (existingPost) {
              console.log('Found existing post, preserving caption data');

              // Use existing caption if it's better than what we have
              if (existingPost.caption && existingPost.caption !== 'Generating content...' &&
                  (!finalCaptionToSave || finalCaptionToSave.length === 0)) {
                finalCaptionToSave = existingPost.caption;
                console.log('Using existing caption from database');
              }

              // Merge content JSON, preserving existing caption parts if they're better
              try {
                const existingContent = JSON.parse(existingPost.content);
                const newContent = JSON.parse(postContent);

                // Use existing caption parts if they're not "Generating..." and new ones are empty/generating
                if (existingContent.captionPart && existingContent.captionPart !== 'Generating...' &&
                    (!newContent.captionPart || newContent.captionPart === 'Generating...' || newContent.captionPart === '')) {
                  newContent.captionPart = existingContent.captionPart;
                  console.log('Preserved existing captionPart');
                }

                if (existingContent.hashtagsPart && existingContent.hashtagsPart !== 'Generating...' &&
                    (!newContent.hashtagsPart || newContent.hashtagsPart === 'Generating...' || newContent.hashtagsPart === '')) {
                  newContent.hashtagsPart = existingContent.hashtagsPart;
                  console.log('Preserved existing hashtagsPart');
                }

                // Update isGenerating to false since we're adding an image
                newContent.isGenerating = false;

                finalContentToSave = JSON.stringify(newContent);

                // Reconstruct caption from preserved parts if needed
                if (!finalCaptionToSave || finalCaptionToSave === 'Generating content...' || finalCaptionToSave.length === 0) {
                  const captionPart = newContent.captionPart || '';
                  const hashtagsPart = newContent.hashtagsPart || '';

                  if (captionPart && hashtagsPart) {
                    finalCaptionToSave = `${captionPart}\n\n${hashtagsPart}`;
                  } else if (captionPart) {
                    finalCaptionToSave = captionPart;
                  } else if (hashtagsPart) {
                    finalCaptionToSave = hashtagsPart;
                  }
                  console.log('Reconstructed caption from preserved parts');
                }
              } catch (error) {
                console.error('Error merging content JSON:', error);
                // Fall back to using the new content as-is
              }
            }
          }

          postResult = await savePost(
            actualUserId,
            finalContentToSave,
            finalCaptionToSave,
            `/api/images/${imageId}`,
            postIdToUse // Use the final post ID from our duplicate check
          );

          if (postResult.error) {
            console.error('Error auto-saving post:', postResult.error);
          } else {
            console.log('Post auto-saved successfully with ID:', postResult.postId);

            // If this is a new post (no postId provided), copy the image to a more predictable filename
            if (!postId && postResult.postId) {
              try {
                // Create a copy of the image with the post ID in the filename for easier reference
                const newFileName = `post_${postResult.postId}.${fileExtension}`;
                const newFilePath = path.join(imagesDir, newFileName);

                // Copy the file
                fs.copyFileSync(filePath, newFilePath);
                console.log(`Created additional copy of image with post ID in filename: ${newFilePath}`);
              } catch (copyErr) {
                console.error('Error creating additional copy of image:', copyErr);
                // This is not critical, so we'll continue even if it fails
              }
            }
          }
        }
      }

      // Return both the image path and the base64 data (for immediate display)
      res.json({
        imageId: imageId,
        imagePath: `/api/images/${imageId}`,
        imageData: `data:${mimeType};base64,${imageData}`,
        postResult: postResult
      });
    } catch (err) {
      console.error('Error saving image to filesystem:', err);
      // Fall back to just returning the base64 data
      res.json({
        imageData: `data:${mimeType};base64,${imageData}`
      });
    }

  } catch (error) {
    console.error('Error generating image:', error);

    // If all API keys failed, return the user's original content with an error message
    if (error.message && error.message.includes('All API keys failed')) {
      return res.status(500).json({
        error: 'All API keys have failed or reached their limits. Please try again later.',
        originalContent: {
          imagePrompt,
          postContent,
          caption
        },
        allKeysFailed: true
      });
    }

    // Extract more detailed error information if available
    let errorMessage = 'Failed to generate image';
    if (error.message) {
      errorMessage = error.message;
    }

    // Check if there's a more detailed error in the cause
    if (error.cause) {
      console.error('Error cause:', error.cause);
      errorMessage += ' - ' + error.cause;
    }

    res.status(500).json({
      error: errorMessage,
      details: error.toString(),
      stack: error.stack
    });
  }
});



// Authentication routes
app.post('/api/auth/register',
  validateBody(registerSchema),
  async (req, res) => {
    try {
      const { email, password, fullName } = req.body;

      const result = await createEmailVerification(email, password, fullName);

      if (result.error) {
        return res.status(400).json({ error: result.error.message });
      }

      res.status(200).json(result.data);
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({ error: 'Registration failed' });
    }
  }
);

// Email verification endpoint
app.post('/api/auth/verify-email',
  validateBody(verifyEmailSchema),
  async (req, res) => {
    try {
      const { email, verificationCode } = req.body;

      const result = await verifyEmailAndCreateAccount(email, verificationCode);

      if (result.error) {
        return res.status(400).json({ error: result.error.message });
      }

      // Track user session with IP for the IP limit system
      try {
        const userId = result.data.user.id;
        const sessionToken = result.data.token;
        const ipAddress = req.ip || req.connection?.remoteAddress || 'unknown';
        const userAgent = req.get('User-Agent') || 'unknown';

        await trackUserSession(userId, sessionToken, ipAddress, userAgent);
        console.log(`Registration session tracked for user ${userId} from IP ${ipAddress}`);
      } catch (sessionError) {
        console.error('Error tracking registration session:', sessionError);
      }

      res.status(201).json(result.data);
    } catch (error) {
      console.error('Email verification error:', error);
      res.status(500).json({ error: 'Email verification failed' });
    }
  }
);

// Resend verification code endpoint
app.post('/api/auth/resend-verification',
  validateBody(resendVerificationSchema),
  async (req, res) => {
    try {
      const { email } = req.body;

      const result = await resendVerificationCode(email);

      if (result.error) {
        return res.status(400).json({ error: result.error.message });
      }

      res.status(200).json(result.data);
    } catch (error) {
      console.error('Resend verification error:', error);
      res.status(500).json({ error: 'Failed to resend verification code' });
    }
  }
);

app.post('/api/auth/login',
  validateBody(loginSchema),
  async (req, res) => {
    try {
      const { email, password } = req.body;

      const result = await loginUser(email, password);

      if (result.error) {
        return res.status(401).json({ error: result.error.message });
      }

      // Track user session
      try {
        const userId = result.data.user.id;
        const sessionToken = result.data.token;
        const ipAddress = req.ip || req.connection?.remoteAddress || 'unknown';
        const userAgent = req.get('User-Agent') || 'unknown';

        await trackUserSession(userId, sessionToken, ipAddress, userAgent);
        console.log(`Session tracked for user ${userId} from IP ${ipAddress}`);
      } catch (sessionError) {
        console.error('Error tracking user session:', sessionError);
        // Don't fail login if session tracking fails
      }

    res.json(result.data);
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// User profile routes
app.get('/api/profile', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;
    const result = await getUserProfile(userId);

    if (result.error) {
      return res.status(404).json({ error: result.error.message });
    }

    res.json(result.data);
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: 'Failed to get profile' });
  }
});

// Force refresh profile endpoint
app.get('/api/profile/refresh', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;

    // Force a fresh database query by bypassing any caching
    const result = await getUserProfile(userId, true);

    if (result.error) {
      return res.status(404).json({ error: result.error.message });
    }

    res.json(result.data);
  } catch (error) {
    console.error('Refresh profile error:', error);
    res.status(500).json({ error: 'Failed to refresh profile' });
  }
});

app.patch('/api/profile', authenticateToken, validateBody(updateProfileSchema), async (req, res) => {
  try {
    const userId = req.user.sub;
    const updates = req.body;

    const result = await updateUserProfile(userId, updates);

    if (result.error) {
      return res.status(400).json({ error: result.error.message });
    }

    res.json(result.data);
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Contact form endpoint
console.log('Registering /api/contact endpoint');
app.post('/api/contact', optionalAuth, validateBody(contactSchema), async (req, res) => {
  try {
    const { name, email, message } = req.body;
    const timestamp = new Date().toISOString();
    const messageId = uuidv4();
    const clientIP = req.ip || req.socket?.remoteAddress || 'unknown';
    const userId = req.user ? req.user.sub : null;

    // Create message object
    const contactMessage = {
      id: messageId,
      name,
      email,
      message,
      timestamp,
      ip: clientIP,
      userId
    };

    // Create messages directory if it doesn't exist
    const messagesDir = path.join(__dirname, '..', 'data', 'messages');
    if (!fs.existsSync(messagesDir)) {
      fs.mkdirSync(messagesDir, { recursive: true });
    }

    // Save to text file
    const messageText = `
=== MESSAGE ${messageId} ===
Date: ${timestamp}
Name: ${name}
Email: ${email}
IP: ${clientIP}
User ID: ${userId || 'Guest'}
Message:
${message}
==============================

`;

    const filename = `messages_${new Date().toISOString().split('T')[0]}.txt`;
    const filepath = path.join(messagesDir, filename);

    fs.appendFileSync(filepath, messageText);

    res.json({ success: true, messageId });
  } catch (error) {
    console.error('Contact form error:', error);
    res.status(500).json({ error: 'Failed to save message' });
  }
});

// PayPal payment routes
app.post('/api/payments/paypal/capture', authenticateToken, async (req, res) => {
  try {
    console.log('PayPal capture endpoint called');
    const userId = req.user.sub;
    const { orderID, planName, billingCycle, paymentDetails } = req.body;

    console.log('Capture request details:', {
      userId,
      orderID,
      planName,
      billingCycle,
      paymentStatus: paymentDetails?.status
    });

    // Skip PayPal verification for now since we already captured on frontend
    // In production, you might want to verify with PayPal, but for testing let's skip this
    console.log('Using payment details from frontend capture');

    // Check if payment was completed (from frontend capture)
    if (paymentDetails?.status !== 'COMPLETED') {
      console.log('Payment not completed, status:', paymentDetails?.status);
      return res.status(400).json({ error: 'Payment not completed' });
    }

    // Get plan details
    console.log('Getting plan details for:', planName, billingCycle);
    const planDetails = getPlanDetails(planName, billingCycle);
    console.log('Plan details:', planDetails);

    // Verify payment amount matches plan price
    const paidAmount = parseFloat(paymentDetails.purchase_units[0].amount.value);
    console.log('Paid amount:', paidAmount, 'Expected:', planDetails.price);

    if (Math.abs(paidAmount - planDetails.price) > 0.01) { // Allow small floating point differences
      console.log('Payment amount mismatch');
      return res.status(400).json({ error: 'Payment amount mismatch' });
    }

    // Calculate next billing date
    const nextBillingDate = getNextBillingDate(billingCycle);
    console.log('Next billing date:', nextBillingDate);

    // Update user's plan
    const updates = {
      plan: planName.toLowerCase(),
      posts_limit: planDetails.posts_limit,
      posts_count: 0, // Reset post count for new billing cycle
      subscription_status: 'active',
      billing_cycle: billingCycle,
      next_billing_date: process.env.DATABASE_TYPE === 'mysql'
        ? nextBillingDate.toISOString().slice(0, 19).replace('T', ' ')
        : nextBillingDate.toISOString(),
      paypal_order_id: orderID,
      updated_at: getCurrentTimestamp()
    };

    console.log('Updating user profile with:', updates);
    const result = await updateUserProfile(userId, updates);

    if (result.error) {
      console.error('Failed to update user profile:', result.error);
      return res.status(500).json({ error: 'Failed to update user plan' });
    }

    // Log the successful payment
    console.log(`Payment successful: User ${userId} upgraded to ${planName} plan`);

    res.json({
      success: true,
      message: `Successfully upgraded to ${planName} plan`,
      plan: updates
    });

  } catch (error) {
    console.error('PayPal capture error:', error);
    res.status(500).json({
      error: 'Payment processing failed',
      details: error.message
    });
  }
});

// Get PayPal configuration for frontend
app.get('/api/payments/paypal/config', (req, res) => {
  try {
    const config = {
      clientId: process.env.PAYPAL_CLIENT_ID,
      environment: process.env.PAYPAL_ENVIRONMENT || 'sandbox'
    };

    if (!config.clientId) {
      console.log('PayPal Client ID not configured in environment variables');
      return res.status(400).json({
        error: 'PayPal not configured',
        message: 'Please add PAYPAL_CLIENT_ID to your .env file'
      });
    }

    res.json(config);
  } catch (error) {
    console.error('PayPal config error:', error);
    res.status(500).json({
      error: 'PayPal configuration error',
      message: error.message
    });
  }
});

// PayPal subscription endpoints
app.post('/api/payments/paypal/subscription-config', authenticateToken, async (req, res) => {
  try {
    const { planName, billingCycle } = req.body;

    console.log('Getting subscription config for:', planName, billingCycle);

    const subscriptionDetails = getSubscriptionPlanDetails(planName, billingCycle);
    console.log('Subscription details:', subscriptionDetails);

    res.json(subscriptionDetails);
  } catch (error) {
    console.error('Subscription config error:', error);
    res.status(400).json({
      error: 'Subscription configuration error',
      message: error.message
    });
  }
});

app.post('/api/payments/paypal/subscription-activate', authenticateToken, async (req, res) => {
  try {
    console.log('PayPal subscription activation endpoint called');
    const userId = req.user.sub;
    const { subscriptionID, planName, billingCycle } = req.body;

    console.log('Subscription activation details:', {
      userId,
      subscriptionID,
      planName,
      billingCycle
    });

    // Get subscription plan details
    const subscriptionDetails = getSubscriptionPlanDetails(planName, billingCycle);
    console.log('Subscription plan details:', subscriptionDetails);

    // Calculate next billing date based on subscription interval
    const now = new Date();
    let nextBillingDate;

    if (subscriptionDetails.frequency === 'MONTH') {
      nextBillingDate = new Date(now.getFullYear(), now.getMonth() + subscriptionDetails.frequency_interval, now.getDate());
    } else if (subscriptionDetails.frequency === 'YEAR') {
      nextBillingDate = new Date(now.getFullYear() + subscriptionDetails.frequency_interval, now.getMonth(), now.getDate());
    } else if (subscriptionDetails.frequency === 'DAY') {
      nextBillingDate = new Date(now.getTime() + (subscriptionDetails.frequency_interval * 24 * 60 * 60 * 1000));
    } else if (subscriptionDetails.frequency === 'WEEK') {
      nextBillingDate = new Date(now.getTime() + (subscriptionDetails.frequency_interval * 7 * 24 * 60 * 60 * 1000));
    } else {
      nextBillingDate = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
    }

    console.log('Next billing date:', nextBillingDate);

    // Update user's plan
    const updates = {
      plan: planName.toLowerCase(),
      posts_limit: subscriptionDetails.posts_limit,
      posts_count: 0, // Reset post count for new billing cycle
      subscription_status: 'active',
      billing_cycle: billingCycle,
      next_billing_date: process.env.DATABASE_TYPE === 'mysql'
        ? nextBillingDate.toISOString().slice(0, 19).replace('T', ' ')
        : nextBillingDate.toISOString(),
      paypal_subscription_id: subscriptionID,
      updated_at: getCurrentTimestamp()
    };

    console.log('Updating user profile with:', updates);
    const result = await updateUserProfile(userId, updates);

    if (result.error) {
      console.error('Failed to update user profile:', result.error);
      return res.status(500).json({ error: 'Failed to update user plan' });
    }

    // Log the successful subscription
    console.log(`Subscription successful: User ${userId} subscribed to ${planName} plan`);

    res.json({
      success: true,
      message: `Successfully subscribed to ${planName} plan`,
      plan: updates,
      isSubscription: true
    });

  } catch (error) {
    console.error('PayPal subscription activation error:', error);
    res.status(500).json({
      error: 'Subscription activation failed',
      details: error.message
    });
  }
});

// PayPal subscription cancel endpoint
app.post('/api/payments/paypal/subscription-cancel', authenticateToken, async (req, res) => {
  try {
    console.log('Cancel endpoint hit, req.user:', req.user);
    console.log('Request body:', req.body);

    const { subscriptionID } = req.body;
    const userId = req.user?.sub;

    console.log('PayPal subscription cancellation endpoint called');
    console.log('Cancellation details:', {
      userId,
      subscriptionID
    });

    if (!userId) {
      console.error('No userId found in token');
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'No user ID found in token'
      });
    }

    if (!subscriptionID) {
      return res.status(400).json({
        error: 'Subscription ID is required',
        message: 'Please provide a valid subscription ID'
      });
    }

    // Get PayPal access token
    console.log('Getting PayPal access token...');
    const accessToken = await getPayPalAccessToken();
    console.log('Access token obtained successfully');

    // Cancel subscription via PayPal API
    const paypalBaseUrl = process.env.PAYPAL_BASE_URL || 'https://api-m.sandbox.paypal.com';
    console.log('PayPal Base URL:', paypalBaseUrl);
    console.log('Cancelling subscription:', subscriptionID);

    const cancelResponse = await fetch(`${paypalBaseUrl}/v1/billing/subscriptions/${subscriptionID}/cancel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
        'PayPal-Request-Id': `CANCEL-${Date.now()}`
      },
      body: JSON.stringify({
        reason: 'Customer requested cancellation'
      })
    });

    if (!cancelResponse.ok) {
      const paypalError = await cancelResponse.json();
      console.error('PayPal cancellation failed:', paypalError);

      // If subscription is already cancelled, still proceed with local update
      if (paypalError.name === 'UNPROCESSABLE_ENTITY' &&
          paypalError.details?.[0]?.issue === 'SUBSCRIPTION_STATUS_INVALID') {
        console.log('Subscription already cancelled at PayPal, updating local status...');
      } else {
        return res.status(400).json({
          error: 'PayPal cancellation failed',
          details: paypalError
        });
      }
    } else {
      console.log('PayPal subscription cancelled successfully');
    }

    // Update user profile - keep current plan but mark as cancelled (non-renewing)
    const updates = {
      subscription_status: 'cancelled', // Mark as cancelled but keep current plan
      paypal_subscription_id: subscriptionID, // Keep the ID for reference
      updated_at: getCurrentTimestamp()
      // Don't change plan, posts_limit, billing_cycle, or next_billing_date
      // User keeps benefits until next_billing_date
    };

    console.log('Updating user profile with:', updates);

    // Update the profile in the database
    const updateResult = await updateUserProfile(userId, updates);

    if (updateResult.error) {
      console.error('Failed to update user profile after cancellation:', updateResult.error);
      return res.status(500).json({
        error: 'Profile update failed after cancellation',
        details: updateResult.error
      });
    }

    console.log(`Subscription cancelled: User ${userId} subscription marked as cancelled`);

    res.json({
      success: true,
      message: 'Subscription cancelled successfully. You will keep your current plan benefits until your next billing date, then be downgraded to the free plan.',
      plan: updateResult.data || updates
    });

  } catch (error) {
    console.error('PayPal subscription cancellation error:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      error: 'Subscription cancellation failed',
      details: error.message,
      stack: error.stack
    });
  }
});

// Admin Routes
// Check if current user is admin
app.get('/api/admin/status', checkAdminStatus, (req, res) => {
  console.log('Admin status endpoint - req.isAdmin:', req.isAdmin);
  res.json({ isAdmin: req.isAdmin });
});

// Get all users with pagination, search, and filtering
app.get('/api/admin/users', authenticateAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      plan = '',
      status = ''
    } = req.query;

    const offset = (page - 1) * limit;

    // Build search and filter conditions
    let whereConditions = [];
    let params = [];

    if (search) {
      whereConditions.push('(p.full_name LIKE ? OR u.email LIKE ?)');
      params.push(`%${search}%`, `%${search}%`);
    }

    if (plan) {
      whereConditions.push('p.plan = ?');
      params.push(plan);
    }

    if (status) {
      whereConditions.push('p.subscription_status = ?');
      params.push(status);
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      JOIN profiles p ON u.id = p.id
      ${whereClause}
    `;
    const totalResult = await getOne(countQuery, params);
    const total = totalResult.total;

    // Get users with pagination
    const usersQuery = `
      SELECT
        u.id, u.email, u.created_at as user_created_at,
        p.full_name, p.plan, p.posts_count, p.posts_limit,
        p.subscription_status, p.billing_cycle, p.next_billing_date,
        p.is_admin, p.created_at, p.updated_at
      FROM users u
      JOIN profiles p ON u.id = p.id
      ${whereClause}
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const users = await getAll(usersQuery, [...params, parseInt(limit), offset]);

    // Get post counts for each user
    const usersWithPosts = await Promise.all(users.map(async user => {
      const postCount = await getOne('SELECT COUNT(*) as count FROM posts WHERE user_id = ?', [user.id]);
      return {
        ...user,
        total_posts_created: postCount.count
      };
    }));

    res.json({
      users: usersWithPosts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Get specific user details
app.get('/api/admin/users/:userId', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;

    // Get user details
    const user = await getOne(`
      SELECT
        u.id, u.email, u.created_at as user_created_at,
        p.full_name, p.plan, p.posts_count, p.posts_limit,
        p.subscription_status, p.billing_cycle, p.next_billing_date,
        p.paypal_subscription_id, p.paypal_order_id,
        p.is_admin, p.created_at, p.updated_at, p.last_post_time
      FROM users u
      JOIN profiles p ON u.id = p.id
      WHERE u.id = ?
    `, [userId]);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Get user's posts
    const posts = await getAll(`
      SELECT id, created_at, updated_at, content, caption, image_url, published
      FROM posts
      WHERE user_id = ?
      ORDER BY created_at DESC
    `, [userId]);

    res.json({
      user,
      posts,
      totalPosts: posts.length
    });
  } catch (error) {
    console.error('Error fetching user details:', error);
    res.status(500).json({ error: 'Failed to fetch user details' });
  }
});

// Update user (admin only)
app.patch('/api/admin/users/:userId', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const updates = req.body;

    // Validate that admin is not removing their own admin status
    if (req.user.sub === userId && updates.is_admin === 0) {
      return res.status(400).json({ error: 'Cannot remove your own admin status' });
    }

    const result = await updateUserProfile(userId, {
      ...updates,
      updated_at: getCurrentTimestamp()
    });

    if (result.error) {
      return res.status(400).json({ error: result.error.message });
    }

    res.json(result.data);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Delete user (admin only)
app.delete('/api/admin/users/:userId', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { run } = await import('./db-operations.js');

    // Prevent admin from deleting themselves
    if (req.user.sub === userId) {
      return res.status(400).json({ error: 'Cannot delete your own account' });
    }

    // Delete user (cascades to posts and profile due to foreign keys)
    const result = await run('DELETE FROM users WHERE id = ?', [userId]);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ success: true, message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
});

// Add/Remove credits (posts)
app.patch('/api/admin/users/:userId/credits', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { credits, action } = req.body; // action: 'add' or 'remove' or 'set'

    const profileResult = await getUserProfile(userId);
    if (profileResult.error) {
      return res.status(404).json({ error: 'User not found' });
    }

    const currentCount = profileResult.data.posts_count;
    let newCount;

    switch (action) {
      case 'add':
        newCount = Math.max(0, currentCount + credits);
        break;
      case 'remove':
        newCount = Math.max(0, currentCount - credits);
        break;
      case 'set':
        newCount = Math.max(0, credits);
        break;
      default:
        return res.status(400).json({ error: 'Invalid action. Use add, remove, or set' });
    }

    const result = await updateUserProfile(userId, {
      posts_count: newCount,
      updated_at: getCurrentTimestamp()
    });

    if (result.error) {
      return res.status(400).json({ error: result.error.message });
    }

    res.json({
      success: true,
      previousCount: currentCount,
      newCount: newCount,
      user: result.data
    });
  } catch (error) {
    console.error('Error updating credits:', error);
    res.status(500).json({ error: 'Failed to update credits' });
  }
});

// Get user's gallery/posts
app.get('/api/admin/users/:userId/gallery', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    // Get total count
    const totalResult = await getOne('SELECT COUNT(*) as total FROM posts WHERE user_id = ?', [userId]);
    const total = totalResult.total;

    // Get posts with pagination
    const posts = await getAll(`
      SELECT id, created_at, updated_at, content, caption, image_url, published
      FROM posts
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `, [userId, parseInt(limit), offset]);

    // Process posts to construct captions from content JSON if needed
    const processedPosts = posts.map(post => {
      let finalCaption = post.caption;

      // If caption is null, try to construct it from content JSON
      if (!finalCaption && post.content) {
        try {
          const parsedContent = JSON.parse(post.content);
          const captionPart = parsedContent.captionPart || '';
          const hashtagsPart = parsedContent.hashtagsPart || '';

          if (captionPart && hashtagsPart) {
            finalCaption = `${captionPart}\n\n${hashtagsPart}`;
          } else if (captionPart) {
            finalCaption = captionPart;
          } else if (hashtagsPart) {
            finalCaption = hashtagsPart;
          }
        } catch (error) {
          // If JSON parsing fails, keep original caption
        }
      }

      return {
        ...post,
        caption: finalCaption
      };
    });

    res.json({
      posts: processedPosts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching user gallery:', error);
    res.status(500).json({ error: 'Failed to fetch user gallery' });
  }
});

// Change user plan
app.patch('/api/admin/users/:userId/plan', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { plan, billingCycle } = req.body;

    console.log('Plan change request:', { userId, plan, billingCycle });
    console.log('Database type:', process.env.DATABASE_TYPE || 'sqlite');

    const validPlans = ['free', 'basic', 'pro', 'ultra'];
    const validCycles = ['monthly', 'yearly'];

    if (!validPlans.includes(plan)) {
      return res.status(400).json({ error: 'Invalid plan' });
    }

    if (billingCycle && !validCycles.includes(billingCycle)) {
      return res.status(400).json({ error: 'Invalid billing cycle' });
    }

    // Get plan limits
    const planLimits = {
      free: 3,
      basic: 50,
      pro: 200,
      ultra: 1000
    };

    // Create timestamp in appropriate format
    const now = new Date();
    const timestamp = process.env.DATABASE_TYPE === 'mysql'
      ? now.toISOString().slice(0, 19).replace('T', ' ')  // MySQL format: YYYY-MM-DD HH:MM:SS
      : now.toISOString();  // SQLite format: ISO string

    const updates = {
      plan,
      posts_limit: planLimits[plan],
      posts_count: 0, // Reset count when changing plan
      subscription_status: plan === 'free' ? 'active' : 'active',
      updated_at: timestamp
    };

    if (billingCycle) {
      updates.billing_cycle = billingCycle;
      // Set next billing date to 1 month/year from now
      const nextBilling = new Date();
      if (billingCycle === 'monthly') {
        nextBilling.setMonth(nextBilling.getMonth() + 1);
      } else {
        nextBilling.setFullYear(nextBilling.getFullYear() + 1);
      }

      // Format next billing date appropriately
      updates.next_billing_date = process.env.DATABASE_TYPE === 'mysql'
        ? nextBilling.toISOString().slice(0, 19).replace('T', ' ')
        : nextBilling.toISOString();
    } else if (plan === 'free') {
      updates.billing_cycle = null;
      updates.next_billing_date = null;
    }

    console.log('Updates to apply:', updates);

    const result = await updateUserProfile(userId, updates);

    console.log('Update result:', result);

    if (result.error) {
      console.error('Update profile error details:', result.error);
      return res.status(400).json({ error: result.error.message });
    }

    res.json({
      success: true,
      message: `User plan changed to ${plan}`,
      user: result.data
    });
  } catch (error) {
    console.error('Error changing user plan:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({ error: 'Failed to change user plan' });
  }
});

// Database debugging endpoint (admin only)
app.get('/api/admin/debug/database', authenticateAdmin, async (req, res) => {
  try {
    console.log('Database debug endpoint called');

    const debugInfo = {
      databaseType: process.env.DATABASE_TYPE || 'sqlite',
      environment: process.env.NODE_ENV || 'development',
      timestamp: new Date().toISOString()
    };

    // Test database connection
    const connectionTest = await testConnection();
    debugInfo.connectionTest = connectionTest;

    // Get database configuration (without sensitive data)
    if (process.env.DATABASE_TYPE === 'mysql') {
      debugInfo.mysqlConfig = {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        database: process.env.DB_NAME || 'socialspark',
        user: process.env.DB_USER || 'root',
        passwordSet: !!process.env.DB_PASSWORD
      };
    }

    // Test basic queries
    try {
      const userCount = await getOne('SELECT COUNT(*) as count FROM users');
      debugInfo.userCount = userCount?.count || 0;
    } catch (error) {
      debugInfo.userCountError = error.message;
    }

    try {
      const profileCount = await getOne('SELECT COUNT(*) as count FROM profiles');
      debugInfo.profileCount = profileCount?.count || 0;
    } catch (error) {
      debugInfo.profileCountError = error.message;
    }

    // Test profile table structure
    try {
      if (process.env.DATABASE_TYPE === 'mysql') {
        const tableInfo = await getAll('DESCRIBE profiles');
        debugInfo.profileTableStructure = tableInfo;
      } else {
        const tableInfo = await getAll("PRAGMA table_info(profiles)");
        debugInfo.profileTableStructure = tableInfo;
      }
    } catch (error) {
      debugInfo.tableStructureError = error.message;
    }

    res.json(debugInfo);
  } catch (error) {
    console.error('Database debug error:', error);
    res.status(500).json({
      error: 'Database debug failed',
      details: error.message,
      stack: error.stack
    });
  }
});

// Test profile update endpoint (admin only)
app.post('/api/admin/debug/test-profile-update', authenticateAdmin, async (req, res) => {
  try {
    const { userId, testField, testValue } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }

    console.log('Testing profile update for user:', userId);

    // First, get the current profile
    const currentProfile = await getUserProfile(userId);
    console.log('Current profile:', currentProfile);

    if (currentProfile.error) {
      return res.status(404).json({ error: 'User profile not found', details: currentProfile.error });
    }

    // Test a simple update
    const testUpdates = {};
    if (testField && testValue !== undefined) {
      testUpdates[testField] = testValue;
    } else {
      // Default test: update the updated_at timestamp
      testUpdates.updated_at = getCurrentTimestamp();
    }

    console.log('Test updates:', testUpdates);

    const updateResult = await updateUserProfile(userId, testUpdates);
    console.log('Update result:', updateResult);

    res.json({
      success: !updateResult.error,
      currentProfile: currentProfile.data,
      testUpdates,
      updateResult,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Test profile update error:', error);
    res.status(500).json({
      error: 'Test profile update failed',
      details: error.message,
      stack: error.stack
    });
  }
});

// Analytics endpoints for admin panel

// Get overall platform statistics
app.get('/api/admin/stats/overview', authenticateAdmin, async (req, res) => {
  try {
    // Get total users
    const totalUsers = await getOne('SELECT COUNT(*) as count FROM users') || { count: 0 };

    // Get total posts
    const totalPosts = await getOne('SELECT COUNT(*) as count FROM posts') || { count: 0 };

    // Get users by plan
    const planDistribution = await getAll(`
      SELECT plan, COUNT(*) as count
      FROM profiles
      GROUP BY plan
    `) || [];

    // Get active subscriptions
    const activeSubscriptions = await getOne(`
      SELECT COUNT(*) as count
      FROM profiles
      WHERE subscription_status = 'active' AND plan != 'free'
    `) || { count: 0 };

    // Get posts created today
    const today = new Date().toISOString().split('T')[0];
    const { DATABASE_TYPE } = process.env;

    let dateCondition;
    if (DATABASE_TYPE === 'mysql') {
      dateCondition = 'DATE(created_at) = ?';
    } else {
      dateCondition = 'DATE(created_at) = ?';
    }

    const postsToday = await getOne(`
      SELECT COUNT(*) as count
      FROM posts
      WHERE ${dateCondition}
    `, [today]) || { count: 0 };

    // Get new users today
    const newUsersToday = await getOne(`
      SELECT COUNT(*) as count
      FROM users
      WHERE ${dateCondition}
    `, [today]) || { count: 0 };

    // Get posts created in the last minute (using same approach as individual user rate limiting)
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000).toISOString();
    const postsInLastMinute = await getOne(`
      SELECT COUNT(*) as count
      FROM posts
      WHERE created_at >= ?
    `, [oneMinuteAgo]) || { count: 0 };

    // Define global rate limits for different plans
    const globalRateLimits = {
      free: 30,  // Global limit for free users (posts per minute across all users)
      basic: 65  // Global limit for basic users (posts per minute across all users)
    };

    res.json({
      totalUsers: totalUsers.count || 0,
      totalPosts: totalPosts.count || 0,
      planDistribution: planDistribution || [],
      activeSubscriptions: activeSubscriptions.count || 0,
      postsToday: postsToday.count || 0,
      newUsersToday: newUsersToday.count || 0,
      postsInLastMinute: postsInLastMinute.count || 0,
      globalRateLimits: globalRateLimits
    });
  } catch (error) {
    console.error('Error fetching overview stats:', error);
    res.status(500).json({ error: 'Failed to fetch overview statistics' });
  }
});

// Get currently online users count
app.get('/api/admin/stats/users-online', authenticateAdmin, async (req, res) => {
  try {
    const result = await getOnlineUsersCount();
    res.json({ count: result });
  } catch (error) {
    console.error('Error fetching online users count:', error);
    res.status(500).json({ error: 'Failed to fetch online users count' });
  }
});

// Get user growth analytics
app.get('/api/admin/stats/user-growth', authenticateAdmin, async (req, res) => {
  try {
    const { period = '30', groupBy = 'day' } = req.query;
    const { DATABASE_TYPE } = process.env;

    let dateFormat, dateGroup;
    let periodCondition;

    // Calculate the date threshold using JavaScript for cross-database compatibility
    const periodDays = parseInt(period);
    const thresholdDate = new Date(Date.now() - periodDays * 24 * 60 * 60 * 1000);

    if (DATABASE_TYPE === 'mysql') {
      // MySQL date formatting
      switch (groupBy) {
        case 'hour':
          dateFormat = 'DATE_FORMAT(created_at, "%Y-%m-%d %H:00:00")';
          break;
        case 'day':
          dateFormat = 'DATE_FORMAT(created_at, "%Y-%m-%d")';
          break;
        case 'week':
          dateFormat = 'DATE_FORMAT(created_at, "%Y-W%u")';
          break;
        case 'month':
          dateFormat = 'DATE_FORMAT(created_at, "%Y-%m")';
          break;
        default:
          dateFormat = 'DATE_FORMAT(created_at, "%Y-%m-%d")';
      }
      periodCondition = 'created_at >= ?';
    } else {
      // SQLite date formatting
      switch (groupBy) {
        case 'hour':
          dateFormat = "strftime('%Y-%m-%d %H:00:00', created_at)";
          break;
        case 'day':
          dateFormat = "strftime('%Y-%m-%d', created_at)";
          break;
        case 'week':
          dateFormat = "strftime('%Y-W%W', created_at)";
          break;
        case 'month':
          dateFormat = "strftime('%Y-%m', created_at)";
          break;
        default:
          dateFormat = "strftime('%Y-%m-%d', created_at)";
      }
      periodCondition = 'created_at >= ?';
    }

    const userGrowth = await getAll(`
      SELECT
        ${dateFormat} as period,
        COUNT(*) as new_users
      FROM users
      WHERE ${periodCondition}
      GROUP BY ${dateFormat}
      ORDER BY period ASC
    `, [thresholdDate.toISOString()]);

    res.json({ userGrowth, period, groupBy });
  } catch (error) {
    console.error('Error fetching user growth stats:', error);
    res.status(500).json({ error: 'Failed to fetch user growth statistics' });
  }
});

// Get post activity analytics
app.get('/api/admin/stats/post-activity', authenticateAdmin, async (req, res) => {
  try {
    const { period = '30', groupBy = 'day' } = req.query;
    const { DATABASE_TYPE } = process.env;

    let dateFormat;

    // Calculate the date threshold using JavaScript for cross-database compatibility
    const periodDays = parseInt(period);
    const thresholdDate = new Date(Date.now() - periodDays * 24 * 60 * 60 * 1000);

    if (DATABASE_TYPE === 'mysql') {
      // MySQL date formatting
      switch (groupBy) {
        case 'hour':
          dateFormat = 'DATE_FORMAT(created_at, "%Y-%m-%d %H:00:00")';
          break;
        case 'day':
          dateFormat = 'DATE_FORMAT(created_at, "%Y-%m-%d")';
          break;
        case 'week':
          dateFormat = 'DATE_FORMAT(created_at, "%Y-W%u")';
          break;
        case 'month':
          dateFormat = 'DATE_FORMAT(created_at, "%Y-%m")';
          break;
        default:
          dateFormat = 'DATE_FORMAT(created_at, "%Y-%m-%d")';
      }
    } else {
      // SQLite date formatting
      switch (groupBy) {
        case 'hour':
          dateFormat = "strftime('%Y-%m-%d %H:00:00', created_at)";
          break;
        case 'day':
          dateFormat = "strftime('%Y-%m-%d', created_at)";
          break;
        case 'week':
          dateFormat = "strftime('%Y-W%W', created_at)";
          break;
        case 'month':
          dateFormat = "strftime('%Y-%m', created_at)";
          break;
        default:
          dateFormat = "strftime('%Y-%m-%d', created_at)";
      }
    }

    const postActivity = await getAll(`
      SELECT
        ${dateFormat} as period,
        COUNT(*) as posts_created
      FROM posts
      WHERE created_at >= ?
      GROUP BY ${dateFormat}
      ORDER BY period ASC
    `, [thresholdDate.toISOString()]);

    // Get posts by plan
    const postsByPlan = await getAll(`
      SELECT
        p.plan,
        COUNT(posts.id) as post_count
      FROM posts
      JOIN profiles p ON posts.user_id = p.id
      WHERE posts.created_at >= ?
      GROUP BY p.plan
    `, [thresholdDate.toISOString()]);

    res.json({ postActivity, postsByPlan, period, groupBy });
  } catch (error) {
    console.error('Error fetching post activity stats:', error);
    res.status(500).json({ error: 'Failed to fetch post activity statistics' });
  }
});

// Get revenue analytics
app.get('/api/admin/stats/revenue', authenticateAdmin, async (req, res) => {
  try {
    const { period = '30', groupBy = 'day' } = req.query;
    const { DATABASE_TYPE } = process.env;

    // Plan pricing (in USD)
    const planPricing = {
      basic: { monthly: 9.99, yearly: 99.99 },
      pro: { monthly: 19.99, yearly: 199.99 },
      ultra: { monthly: 39.99, yearly: 399.99 }
    };

    let dateFormat;

    // Calculate the date threshold using JavaScript for cross-database compatibility
    const periodDays = parseInt(period);
    const thresholdDate = new Date(Date.now() - periodDays * 24 * 60 * 60 * 1000);

    if (DATABASE_TYPE === 'mysql') {
      // MySQL date formatting
      switch (groupBy) {
        case 'day':
          dateFormat = 'DATE_FORMAT(created_at, "%Y-%m-%d")';
          break;
        case 'week':
          dateFormat = 'DATE_FORMAT(created_at, "%Y-W%u")';
          break;
        case 'month':
          dateFormat = 'DATE_FORMAT(created_at, "%Y-%m")';
          break;
        default:
          dateFormat = 'DATE_FORMAT(created_at, "%Y-%m-%d")';
      }
    } else {
      // SQLite date formatting
      switch (groupBy) {
        case 'day':
          dateFormat = "strftime('%Y-%m-%d', created_at)";
          break;
        case 'week':
          dateFormat = "strftime('%Y-W%W', created_at)";
          break;
        case 'month':
          dateFormat = "strftime('%Y-%m', created_at)";
          break;
        default:
          dateFormat = "strftime('%Y-%m-%d', created_at)";
      }
    }

    // Get subscription data
    const subscriptions = await getAll(`
      SELECT
        plan,
        billing_cycle,
        ${dateFormat} as period,
        COUNT(*) as subscription_count
      FROM profiles
      WHERE plan != 'free'
        AND subscription_status = 'active'
        AND created_at >= ?
      GROUP BY plan, billing_cycle, ${dateFormat}
      ORDER BY period ASC
    `, [thresholdDate.toISOString()]);

    // Calculate estimated revenue
    const revenueData = subscriptions.map(sub => {
      const pricing = planPricing[sub.plan];
      const revenue = pricing ? pricing[sub.billing_cycle] * sub.subscription_count : 0;
      return {
        ...sub,
        estimated_revenue: revenue
      };
    });

    // Get total revenue by plan
    const totalRevenueByPlanData = await getAll(`
      SELECT
        plan,
        billing_cycle,
        COUNT(*) as active_subscriptions
      FROM profiles
      WHERE plan != 'free' AND subscription_status = 'active'
      GROUP BY plan, billing_cycle
    `);

    const totalRevenueByPlan = totalRevenueByPlanData.map(sub => {
      const pricing = planPricing[sub.plan];
      const revenue = pricing ? pricing[sub.billing_cycle] * sub.active_subscriptions : 0;
      return {
        ...sub,
        estimated_monthly_revenue: revenue
      };
    });

    res.json({
      revenueData,
      totalRevenueByPlan,
      period,
      groupBy,
      planPricing
    });
  } catch (error) {
    console.error('Error fetching revenue stats:', error);
    res.status(500).json({ error: 'Failed to fetch revenue statistics' });
  }
});





// Posts routes
app.post('/api/posts', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;
    const { content, caption, imageUrl, postId } = req.body;

    if (!content) {
      return res.status(400).json({ error: 'Content is required' });
    }

    // If postId is provided, update the existing post
    const result = await savePost(userId, content, caption, imageUrl, postId);

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    res.status(201).json(result);
  } catch (error) {
    console.error('Create post error:', error);
    res.status(500).json({ error: 'Failed to create post' });
  }
});

// Check rate limit for current user before generating content
app.get('/api/check-rate-limit', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;

    // Get user profile to check plan
    const profileResult = await getUserProfile(userId);
    if (profileResult.error) {
      return res.status(404).json({ error: 'User profile not found' });
    }

    const profile = profileResult.data;

    // Get rate limit for user's plan
    const rateLimit = getRateLimit(profile.plan);

    // Query posts created in the last minute for this user
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000).toISOString();
    const recentPosts = await getAll(`
      SELECT created_at
      FROM posts
      WHERE user_id = ?
      AND created_at >= ?
      ORDER BY created_at ASC
    `, [userId, oneMinuteAgo]);

    const postsInLastMinute = recentPosts.length;

    // Check if user has exceeded rate limit
    if (postsInLastMinute >= rateLimit) {
      // Find the oldest post that needs to expire for user to make another post
      // We need to wait until the oldest post is more than 60 seconds old
      const oldestPostTime = new Date(recentPosts[0].created_at); // First post since we ordered ASC
      const now = new Date();
      const ageOfOldestPost = (now - oldestPostTime) / 1000; // seconds

      // Calculate how much longer we need to wait for the oldest post to be 60+ seconds old
      const waitTimeSeconds = Math.ceil(60 - ageOfOldestPost);

      // If wait time is negative or zero, something is wrong with our calculation, default to 1
      const actualWaitTime = waitTimeSeconds > 0 ? waitTimeSeconds : 1;

      return res.status(429).json({
        rateLimitExceeded: true,
        error: `Rate limit exceeded. ${profile.plan.toUpperCase()} plan allows ${rateLimit} post${rateLimit > 1 ? 's' : ''} per minute. Please wait ${actualWaitTime} seconds.`,
        postsInLastMinute,
        rateLimit,
        waitTimeSeconds: actualWaitTime,
        plan: profile.plan,
        debug: {
          oldestPostTime: oldestPostTime.toISOString(),
          now: now.toISOString(),
          ageOfOldestPost: Math.round(ageOfOldestPost),
          calculatedWait: waitTimeSeconds
        }
      });
    }

    res.json({
      rateLimitExceeded: false,
      postsInLastMinute,
      rateLimit,
      plan: profile.plan,
      remainingPosts: rateLimit - postsInLastMinute
    });

  } catch (error) {
    console.error('Error checking rate limit:', error);
    res.status(500).json({ error: 'Failed to check rate limit' });
  }
});

// Get recent posts count for current user (must be before the :postId route)
app.get('/api/posts/recent-count', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;
    const { minutes = 1 } = req.query;

    // Query posts created in the last N minutes for this user (using same approach as rate limiting)
    const minutesAgo = new Date(Date.now() - parseInt(minutes) * 60 * 1000).toISOString();
    const recentPosts = await getAll(`
      SELECT COUNT(*) as count
      FROM posts
      WHERE user_id = ?
      AND created_at >= ?
    `, [userId, minutesAgo]);

    const count = recentPosts[0]?.count || 0;

    res.json({
      count,
      timeframe: `${minutes} minute(s)`,
      userId
    });
  } catch (error) {
    console.error('Error fetching recent posts count:', error);
    res.status(500).json({ error: 'Failed to fetch recent posts count' });
  }
});

// Get all posts for the authenticated user with pagination
app.get('/api/posts', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;
    const { page = 1, limit = 6 } = req.query; // Default to 6 posts per page
    const offset = (page - 1) * limit;

    // Get total count of posts for pagination info
    const totalResult = await getOne('SELECT COUNT(*) as total FROM posts WHERE user_id = ?', [userId]);
    const total = totalResult.total;

    // Get posts for the current page from the database
    const posts = await getAll('SELECT * FROM posts WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?', [userId, parseInt(limit), offset]);

    // Check if we have a posts directory for this user
    const postsDir = path.join(__dirname, '..', 'data', 'posts');
    const userPostsDir = path.join(postsDir, 'db', userId);

    // Create the directory if it doesn't exist
    if (!fs.existsSync(userPostsDir)) {
      fs.mkdirSync(userPostsDir, { recursive: true });
      console.log(`Created user posts directory: ${userPostsDir}`);
    }

    // Get all JSON files for this user
    let postFiles = [];
    try {
      if (fs.existsSync(userPostsDir)) {
        postFiles = fs.readdirSync(userPostsDir)
          .filter(file => file.endsWith('.json'))
          .map(file => {
            try {
              const filePath = path.join(userPostsDir, file);
              const fileContent = fs.readFileSync(filePath, 'utf8');
              return JSON.parse(fileContent);
            } catch (err) {
              console.error(`Error reading post file ${file}:`, err);
              return null;
            }
          })
          .filter(data => data !== null);
      }
    } catch (err) {
      console.error(`Error reading user posts directory ${userPostsDir}:`, err);
    }

    // Create a map of post IDs to file data for quick lookup
    const postFileMap = new Map();
    postFiles.forEach(fileData => {
      if (fileData && fileData.id) {
        postFileMap.set(fileData.id, fileData);
      }
    });

    // Format the response
    const formattedPosts = await Promise.all(posts.map(async post => {
      // Parse the content JSON
      let parsedContent = {};
      try {
        parsedContent = JSON.parse(post.content);

        // Check if we have file data for this post
        const fileData = postFileMap.get(post.id);
        if (fileData) {
          // Merge the content from the file with the database content
          if (fileData.content) {
            parsedContent = {
              ...parsedContent,
              ...fileData.content
            };
          }

          // Use caption parts from the file if available
          if (fileData.captionPart) {
            parsedContent.captionPart = fileData.captionPart;
          }

          if (fileData.hashtagsPart) {
            parsedContent.hashtagsPart = fileData.hashtagsPart;
          }
        }

        // Add caption and hashtag parts if they don't exist
        if (post.caption && (!parsedContent.captionPart || !parsedContent.hashtagsPart)) {
          const parts = post.caption.split('\n\n');
          if (parts.length >= 2) {
            parsedContent.captionPart = parts[0];
            parsedContent.hashtagsPart = parts[1];
          } else {
            parsedContent.captionPart = post.caption;
            parsedContent.hashtagsPart = '';
          }

          // If we have a file for this post but no caption parts, update the file
          if (postFileMap.has(post.id) && (!postFileMap.get(post.id).captionPart || !postFileMap.get(post.id).hashtagsPart)) {
            try {
              const fileData = postFileMap.get(post.id);
              fileData.captionPart = parsedContent.captionPart;
              fileData.hashtagsPart = parsedContent.hashtagsPart;

              const filePath = path.join(userPostsDir, `${post.id}.json`);
              fs.writeFileSync(filePath, JSON.stringify(fileData, null, 2));
              console.log(`Updated caption parts in file for post ${post.id}`);
            } catch (updateErr) {
              console.error(`Error updating file for post ${post.id}:`, updateErr);
            }
          }
        }
      } catch (err) {
        console.error('Error parsing post content:', err);
      }

      // Get the image data if available
      let imageData = null;

      // First check if there's a post-specific image file
      const postImagePath = path.join(imagesDir, `post_${post.id}.jpeg`);
      const postImagePathPng = path.join(imagesDir, `post_${post.id}.png`);

      if (fs.existsSync(postImagePath)) {
        try {
          const fileData = fs.readFileSync(postImagePath);
          imageData = `data:image/jpeg;base64,${fileData.toString('base64')}`;
        } catch (err) {
          console.error(`Error reading post-specific image file ${postImagePath}:`, err);
        }
      } else if (fs.existsSync(postImagePathPng)) {
        try {
          const fileData = fs.readFileSync(postImagePathPng);
          imageData = `data:image/png;base64,${fileData.toString('base64')}`;
        } catch (err) {
          console.error(`Error reading post-specific PNG image file ${postImagePathPng}:`, err);
        }
      } else if (post.image_url) {
        // Fall back to the original image URL from the database
        const imageId = post.image_url.split('/').pop();

        try {
          // Find the image file
          const files = fs.readdirSync(imagesDir);
          const imageFile = files.find(file => file.startsWith(imageId));

          if (imageFile) {
            const filePath = path.join(imagesDir, imageFile);
            try {
              const fileData = fs.readFileSync(filePath);
              const ext = path.extname(imageFile).toLowerCase();
              let mimeType = 'image/jpeg'; // Default

              if (ext === '.png') mimeType = 'image/png';
              else if (ext === '.gif') mimeType = 'image/gif';
              else if (ext === '.webp') mimeType = 'image/webp';

              imageData = `data:${mimeType};base64,${fileData.toString('base64')}`;

              // Create a post-specific copy of the image if it doesn't exist
              try {
                const newFileName = `post_${post.id}${ext}`;
                const newFilePath = path.join(imagesDir, newFileName);

                if (!fs.existsSync(newFilePath)) {
                  fs.copyFileSync(filePath, newFilePath);
                  console.log(`Created post-specific copy of image for post ${post.id}`);
                }
              } catch (copyErr) {
                console.error(`Error creating post-specific copy of image for post ${post.id}:`, copyErr);
              }
            } catch (err) {
              console.error(`Error reading image file ${filePath}:`, err);
            }
          }
        } catch (fsError) {
          console.error(`Error accessing image directory for post ${post.id}:`, fsError);
        }
      }

      // If we don't have a JSON file for this post, create one
      if (!postFileMap.has(post.id)) {
        try {
          const postData = {
            id: post.id,
            userId: userId,
            content: parsedContent,
            caption: post.caption,
            captionPart: parsedContent.captionPart || '',
            hashtagsPart: parsedContent.hashtagsPart || '',
            imageUrl: post.image_url,
            createdAt: post.created_at,
            updatedAt: new Date().toISOString()
          };

          const filePath = path.join(userPostsDir, `${post.id}.json`);
          fs.writeFileSync(filePath, JSON.stringify(postData, null, 2));
          console.log(`Created JSON file for post ${post.id}`);
        } catch (fileErr) {
          console.error(`Error creating JSON file for post ${post.id}:`, fileErr);
        }
      }

      // Construct caption from content JSON if database caption is null
      let finalCaption = post.caption;
      if (!finalCaption && (parsedContent.captionPart || parsedContent.hashtagsPart)) {
        const captionPart = parsedContent.captionPart || '';
        const hashtagsPart = parsedContent.hashtagsPart || '';

        if (captionPart && hashtagsPart) {
          finalCaption = `${captionPart}\n\n${hashtagsPart}`;
        } else if (captionPart) {
          finalCaption = captionPart;
        } else if (hashtagsPart) {
          finalCaption = hashtagsPart;
        }
      }

      return {
        id: post.id,
        content: parsedContent,
        caption: finalCaption,
        imageUrl: post.image_url,
        imageData: imageData,
        createdAt: post.created_at
      };
    }));

    res.json({
      posts: formattedPosts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get posts error:', error);
    res.status(500).json({ error: 'Failed to get posts' });
  }
});

// Get post by ID
app.get('/api/posts/:postId', async (req, res) => {
  try {
    const { postId } = req.params;
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!postId) {
      return res.status(400).json({ error: 'Post ID is required' });
    }

    console.log(`Fetching post with ID: ${postId}, auth token present: ${!!token}`);

    // Get the post from the database
    const post = await getOne('SELECT * FROM posts WHERE id = ?', [postId]);

    if (!post) {
      console.log(`Post not found in database: ${postId}`);
      return res.status(404).json({ error: 'Post not found' });
    }

    console.log(`Post found in database: ${postId}, user_id: ${post.user_id}`);

    // If user is authenticated, verify they own the post
    let isOwner = false;
    let userId = null;
    if (token) {
      try {
        const user = verifyToken(token);
        if (user) {
          userId = user.sub;
          console.log(`User authenticated: ${userId}, post owner: ${post.user_id}`);
          isOwner = userId === post.user_id;

          // If not the owner, return 403
          if (!isOwner) {
            console.log(`Access denied: User ${userId} attempted to access post ${postId} owned by ${post.user_id}`);
            return res.status(403).json({ error: 'You do not have permission to access this post' });
          }
        } else {
          console.log(`Invalid token provided for post ${postId}`);
          return res.status(401).json({ error: 'Invalid authentication token' });
        }
      } catch (tokenError) {
        console.error(`Error verifying token for post ${postId}:`, tokenError);
        return res.status(401).json({ error: 'Invalid authentication token' });
      }
    } else {
      console.log(`No authentication token provided for post ${postId}`);
      // We'll still return the post data but with a warning
      console.log(`Warning: Unauthenticated access to post ${postId}`);
    }

    // Check if we have a JSON file for this post
    let postFileData = null;
    const postsDir = path.join(__dirname, '..', 'data', 'posts');
    const userPostsDir = path.join(postsDir, 'db', post.user_id);
    const postFilePath = path.join(userPostsDir, `${postId}.json`);

    if (fs.existsSync(postFilePath)) {
      try {
        console.log(`Found post JSON file: ${postFilePath}`);
        const fileContent = fs.readFileSync(postFilePath, 'utf8');
        postFileData = JSON.parse(fileContent);
        console.log('Successfully loaded post data from JSON file');
      } catch (err) {
        console.error(`Error reading post file ${postFilePath}:`, err);
      }
    } else {
      console.log(`No JSON file found for post ${postId}, will use database data only`);

      // Create the directory if it doesn't exist
      if (!fs.existsSync(userPostsDir)) {
        fs.mkdirSync(userPostsDir, { recursive: true });
        console.log(`Created user posts directory: ${userPostsDir}`);
      }
    }

    // Get the image data if available
    let imageData = null;

    // First check if there's a post-specific image file
    const postImagePath = path.join(imagesDir, `post_${postId}.jpeg`);
    const postImagePathPng = path.join(imagesDir, `post_${postId}.png`);

    if (fs.existsSync(postImagePath)) {
      console.log(`Found post-specific image file: ${postImagePath}`);
      try {
        const fileData = fs.readFileSync(postImagePath);
        imageData = `data:image/jpeg;base64,${fileData.toString('base64')}`;
        console.log(`Image data loaded successfully from post-specific file, data length: ${imageData.length}`);
      } catch (err) {
        console.error(`Error reading post-specific image file ${postImagePath}:`, err);
      }
    } else if (fs.existsSync(postImagePathPng)) {
      console.log(`Found post-specific PNG image file: ${postImagePathPng}`);
      try {
        const fileData = fs.readFileSync(postImagePathPng);
        imageData = `data:image/png;base64,${fileData.toString('base64')}`;
        console.log(`Image data loaded successfully from post-specific PNG file, data length: ${imageData.length}`);
      } catch (err) {
        console.error(`Error reading post-specific PNG image file ${postImagePathPng}:`, err);
      }
    } else if (post.image_url) {
      // Fall back to the original image URL from the database
      const imageId = post.image_url.split('/').pop();
      console.log(`Fetching image data for ID: ${imageId}`);

      try {
        // Find the image file
        const files = fs.readdirSync(imagesDir);
        const imageFile = files.find(file => file.startsWith(imageId));

        if (imageFile) {
          console.log(`Image file found: ${imageFile}`);
          const filePath = path.join(imagesDir, imageFile);
          try {
            const fileData = fs.readFileSync(filePath);
            const ext = path.extname(imageFile).toLowerCase();
            let mimeType = 'image/jpeg'; // Default

            if (ext === '.png') mimeType = 'image/png';
            else if (ext === '.gif') mimeType = 'image/gif';
            else if (ext === '.webp') mimeType = 'image/webp';

            imageData = `data:${mimeType};base64,${fileData.toString('base64')}`;
            console.log(`Image data loaded successfully for ${imageId}, data length: ${imageData.length}`);

            // If we found the image but don't have a post-specific copy, create one
            if (isOwner && userId) {
              try {
                const newFileName = `post_${postId}${ext}`;
                const newFilePath = path.join(imagesDir, newFileName);

                // Copy the file if it doesn't already exist
                if (!fs.existsSync(newFilePath)) {
                  fs.copyFileSync(filePath, newFilePath);
                  console.log(`Created post-specific copy of image: ${newFilePath}`);
                }
              } catch (copyErr) {
                console.error('Error creating post-specific copy of image:', copyErr);
              }
            }
          } catch (err) {
            console.error(`Error reading image file ${filePath}:`, err);
          }
        } else {
          console.log(`Image file not found for ID: ${imageId}`);

          // Check if the image_url is a full data URL
          if (post.image_url.startsWith('data:')) {
            console.log('Image URL is a data URL, using directly');
            imageData = post.image_url;
          }
        }
      } catch (fsError) {
        console.error(`Error accessing image directory for post ${postId}:`, fsError);
      }
    } else {
      console.log(`No image URL found for post ${postId}`);
    }

    // Parse the content JSON from the database
    let parsedContent = {};
    try {
      // Only parse if it's a string
      if (typeof post.content === 'string') {
        parsedContent = JSON.parse(post.content);
        console.log('Successfully parsed post content JSON from database');
      } else {
        console.log('Post content is not a string, using as is');
        parsedContent = post.content;
      }

      // If we have post file data, merge it with the database content
      if (postFileData && postFileData.content) {
        console.log('Merging content from JSON file with database content');
        parsedContent = {
          ...parsedContent,
          ...postFileData.content
        };
      }

      // Extract caption and hashtags for the frontend
      let captionPart = '';
      let hashtagsPart = '';

      // Priority 1: Use from JSON file if available
      if (postFileData && postFileData.captionPart) {
        captionPart = postFileData.captionPart;
        console.log('Using captionPart from JSON file');
      }
      if (postFileData && postFileData.hashtagsPart) {
        hashtagsPart = postFileData.hashtagsPart;
        console.log('Using hashtagsPart from JSON file');
      }

      // Priority 2: Use from parsed content if available
      if (!captionPart && parsedContent.captionPart) {
        captionPart = parsedContent.captionPart;
        console.log('Using captionPart from parsed content');
      }
      if (!hashtagsPart && parsedContent.hashtagsPart) {
        hashtagsPart = parsedContent.hashtagsPart;
        console.log('Using hashtagsPart from parsed content');
      }

      // Priority 3: Split the database caption if we still don't have them
      if (post.caption && (!captionPart || !hashtagsPart)) {
        console.log('Splitting database caption into parts');
        const parts = post.caption.split('\n\n');
        if (parts.length >= 2) {
          if (!captionPart) captionPart = parts[0];
          if (!hashtagsPart) hashtagsPart = parts[1];
        } else {
          // If no clear separation, assume it's all caption
          if (!captionPart) captionPart = post.caption;
          if (!hashtagsPart) hashtagsPart = '';
        }
      }

      // Set the parts in parsedContent for the frontend
      parsedContent.captionPart = captionPart;
      parsedContent.hashtagsPart = hashtagsPart;

      console.log('Final caption parts:', {
        captionPart: captionPart ?
          (captionPart.substring(0, Math.min(50, captionPart.length)) +
           (captionPart.length > 50 ? '...' : '')) : 'empty',
        hashtagsPart: hashtagsPart ?
          (hashtagsPart.substring(0, Math.min(50, hashtagsPart.length)) +
           (hashtagsPart.length > 50 ? '...' : '')) : 'empty'
      });

    } catch (err) {
      console.error('Error parsing post content:', err);
      // If parsing fails, return the raw content
      parsedContent = post.content;
    }

    // If we have post file data but no JSON file, create one
    if (isOwner && userId && !postFileData) {
      try {
        console.log(`Creating JSON file for post ${postId}`);

        const postData = {
          id: postId,
          userId: post.user_id,
          content: parsedContent,
          caption: post.caption,
          captionPart: parsedContent.captionPart || '',
          hashtagsPart: parsedContent.hashtagsPart || '',
          imageUrl: post.image_url,
          createdAt: post.created_at,
          updatedAt: new Date().toISOString()
        };

        fs.writeFileSync(postFilePath, JSON.stringify(postData, null, 2));
        console.log(`Created JSON file for post ${postId}: ${postFilePath}`);
      } catch (fileErr) {
        console.error(`Error creating JSON file for post ${postId}:`, fileErr);
      }
    }

    // Log the raw post data for debugging
    console.log('Raw post data from database:', {
      id: post.id,
      user_id: post.user_id,
      content: typeof post.content === 'string' ? post.content.substring(0, 100) + '...' : 'not a string',
      caption: post.caption ? post.caption.substring(0, 100) + '...' : 'null',
      image_url: post.image_url
    });

    // Construct caption from content JSON if database caption is null
    let finalCaption = post.caption;
    if (!finalCaption && (parsedContent.captionPart || parsedContent.hashtagsPart)) {
      const captionPart = parsedContent.captionPart || '';
      const hashtagsPart = parsedContent.hashtagsPart || '';

      if (captionPart && hashtagsPart) {
        finalCaption = `${captionPart}\n\n${hashtagsPart}`;
      } else if (captionPart) {
        finalCaption = captionPart;
      } else if (hashtagsPart) {
        finalCaption = hashtagsPart;
      }
    }

    // Format the response with proper content structure for the frontend
    const response = {
      id: post.id,
      userId: post.user_id,
      content: {
        ...parsedContent,
        // Ensure we have the required fields for the frontend
        CAPTION: parsedContent.captionPart || '',
        HASHTAGS: parsedContent.hashtagsPart || '',
        IMAGE_PROMPT: parsedContent.IMAGE_PROMPT || 'Image prompt not available for saved posts'
      },
      caption: finalCaption,
      imageUrl: post.image_url,
      imageData: imageData,
      createdAt: post.created_at,
      isOwner: isOwner,
      // Add a flag to indicate if image loading was attempted but failed
      imageLoadAttempted: !!post.image_url
    };

    console.log(`Successfully returning post data for ${postId}:`, {
      imageDataPresent: !!imageData,
      contentType: typeof parsedContent,
      captionPresent: !!post.caption,
      frontendCaption: response.content.CAPTION ? response.content.CAPTION.substring(0, 50) + '...' : 'empty',
      frontendHashtags: response.content.HASHTAGS ? response.content.HASHTAGS.substring(0, 50) + '...' : 'empty'
    });
    res.json(response);
  } catch (error) {
    console.error('Get post error:', error);
    res.status(500).json({ error: 'Failed to get post', details: error.message });
  }
});

// Delete post by ID
app.delete('/api/posts/:postId', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;
    const { postId } = req.params;

    if (!postId) {
      return res.status(400).json({ error: 'Post ID is required' });
    }

    // Get the post from the database to verify ownership
    const post = await getOne('SELECT * FROM posts WHERE id = ?', [postId]);

    if (!post) {
      return res.status(404).json({ error: 'Post not found' });
    }

    // Verify the user owns the post
    if (post.user_id !== userId) {
      return res.status(403).json({ error: 'You do not have permission to delete this post' });
    }

    // Delete the post from the database
    const { run } = await import('./db-operations.js');
    await run('DELETE FROM posts WHERE id = ?', [postId]);

    // Delete the post JSON file if it exists
    const postsDir = path.join(__dirname, '..', 'data', 'posts');
    const userPostsDir = path.join(postsDir, 'db', userId);
    const postFilePath = path.join(userPostsDir, `${postId}.json`);

    if (fs.existsSync(postFilePath)) {
      try {
        fs.unlinkSync(postFilePath);
        console.log(`Deleted post JSON file: ${postFilePath}`);
      } catch (err) {
        console.error(`Error deleting post JSON file: ${postFilePath}`, err);
      }
    }

    // Delete the post-specific image files if they exist
    const postImagePath = path.join(imagesDir, `post_${postId}.jpeg`);
    const postImagePathPng = path.join(imagesDir, `post_${postId}.png`);

    if (fs.existsSync(postImagePath)) {
      try {
        fs.unlinkSync(postImagePath);
        console.log(`Deleted post-specific image file: ${postImagePath}`);
      } catch (err) {
        console.error(`Error deleting post-specific image file: ${postImagePath}`, err);
      }
    }

    if (fs.existsSync(postImagePathPng)) {
      try {
        fs.unlinkSync(postImagePathPng);
        console.log(`Deleted post-specific PNG image file: ${postImagePathPng}`);
      } catch (err) {
        console.error(`Error deleting post-specific PNG image file: ${postImagePathPng}`, err);
      }
    }

    // Delete the original image file if it exists
    if (post.image_url) {
      const imageId = post.image_url.split('/').pop();

      // Find the image file
      const files = fs.readdirSync(imagesDir);
      const imageFile = files.find(file => file.startsWith(imageId));

      if (imageFile) {
        const filePath = path.join(imagesDir, imageFile);
        try {
          fs.unlinkSync(filePath);
          console.log(`Deleted original image file: ${filePath}`);
        } catch (err) {
          console.error(`Error deleting original image file: ${filePath}`, err);
        }
      }
    }

    res.json({ success: true, message: 'Post deleted successfully' });
  } catch (error) {
    console.error('Delete post error:', error);
    res.status(500).json({ error: 'Failed to delete post' });
  }
});

// Serve images endpoint
app.get('/api/images/:imageId', (req, res) => {
  try {
    const imageId = req.params.imageId;

    // Validate imageId to prevent directory traversal attacks
    if (!imageId || imageId.includes('/') || imageId.includes('\\')) {
      return res.status(400).json({ error: 'Invalid image ID' });
    }

    // Find the image file
    const files = fs.readdirSync(imagesDir);
    const imageFile = files.find(file => file.startsWith(imageId));

    if (!imageFile) {
      return res.status(404).json({ error: 'Image not found' });
    }

    const filePath = path.join(imagesDir, imageFile);

    // Determine content type based on file extension
    const ext = path.extname(imageFile).toLowerCase();
    let contentType = 'image/jpeg'; // Default

    if (ext === '.png') contentType = 'image/png';
    else if (ext === '.gif') contentType = 'image/gif';
    else if (ext === '.webp') contentType = 'image/webp';

    // Set appropriate headers and send the file
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year

    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('Error serving image:', error);
    res.status(500).json({ error: 'Failed to serve image' });
  }
});

// Health check endpoint
app.get('/api/health', async (_req, res) => {
  try {
    const dbConnected = await testConnection();
    const { getDatabaseType } = await import('./db-operations.js');
    res.json({
      status: 'ok',
      database: dbConnected ? 'connected' : 'disconnected',
      type: getDatabaseType()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      database: 'disconnected',
      error: error.message
    });
  }
});

// PayPal Webhook endpoint
app.post('/api/webhooks/paypal', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    console.log('PayPal webhook received');
    console.log('Headers:', req.headers);

    // Verify webhook signature (basic validation for now)
    const isValid = verifyWebhookSignature(req.headers, req.body, process.env.PAYPAL_WEBHOOK_ID);

    if (!isValid) {
      console.error('Invalid webhook signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }

    // Parse the webhook body
    let eventData;
    try {
      eventData = JSON.parse(req.body);
    } catch (parseError) {
      console.error('Failed to parse webhook body:', parseError);
      return res.status(400).json({ error: 'Invalid JSON' });
    }

    console.log('Webhook event type:', eventData.event_type);
    console.log('Webhook event data:', JSON.stringify(eventData, null, 2));

    // Handle the webhook event
    const result = await handleWebhookEvent(eventData.event_type, eventData);

    if (result.success) {
      console.log('Webhook processed successfully:', result.message);
      res.json({ success: true, message: result.message });
    } else {
      console.error('Webhook processing failed:', result.error);
      res.status(500).json({ error: result.error });
    }

  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Manual subscription sync endpoint (admin only)
app.post('/api/admin/sync-subscriptions', authenticateAdmin, async (req, res) => {
  try {
    console.log('Manual subscription sync triggered by admin');
    const result = await runSubscriptionMaintenance();

    res.json({
      success: true,
      message: 'Subscription sync completed',
      results: result
    });
  } catch (error) {
    console.error('Manual subscription sync error:', error);
    res.status(500).json({
      error: 'Subscription sync failed',
      details: error.message
    });
  }
});

// Initialize database and start server
const startServer = async () => {
  console.log('Initializing database...');
  let dbInitialized = false;

  try {
    await initializeDatabase();
    dbInitialized = await testConnection();

    if (dbInitialized) {
      console.log('Database initialized successfully');
      console.log(`Using ${process.env.DATABASE_TYPE || 'sqlite'} database`);
    } else {
      throw new Error('Database connection test failed');
    }
  } catch (error) {
    console.error('Failed to initialize database:', error);
    console.warn('Server will start in fallback mode.');
    console.warn('API endpoints requiring database access will return mock data.');

    // Add middleware to handle database-dependent routes in fallback mode
    app.use(['/api/auth', '/api/profile', '/api/posts'], (req, res, next) => {
      // For GET requests, return mock data
      if (req.method === 'GET') {
        if (req.path.includes('/profile')) {
          return res.json({
            id: 'mock-user-id',
            full_name: 'Demo User',
            plan: 'free',
            posts_count: 0,
            posts_limit: 3,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            last_post_time: null
          });
        }

        if (req.path.includes('/posts')) {
          return res.json([]);
        }
      }

      // For POST/PATCH/DELETE requests, simulate success
      if (['POST', 'PATCH', 'DELETE'].includes(req.method)) {
        return res.json({ success: true, message: 'Operation simulated in fallback mode' });
      }

      next();
    });
  }

  // Start the server
  app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`API available at http://localhost:${PORT}/api/health`);
    console.log(`Using ${process.env.DATABASE_TYPE || 'sqlite'} database`);

  // Start scheduled subscription maintenance (runs every hour)
  if (dbInitialized) {
    console.log('Starting subscription maintenance scheduler...');

    // Run immediately on startup
    setTimeout(async () => {
      try {
        console.log('Running initial subscription maintenance...');
        await runSubscriptionMaintenance();
        console.log('Initial subscription maintenance completed');
      } catch (error) {
        console.error('Initial subscription maintenance failed:', error);
      }
    }, 30000); // Wait 30 seconds after startup

    // Then run every hour
    setInterval(async () => {
      try {
        console.log('Running scheduled subscription maintenance...');
        await runSubscriptionMaintenance();
        console.log('Scheduled subscription maintenance completed');
      } catch (error) {
        console.error('Scheduled subscription maintenance failed:', error);
      }
    }, 60 * 60 * 1000); // Every hour

    console.log('Subscription maintenance scheduler started');
  } else {
    console.log('Database not initialized - skipping subscription maintenance scheduler');
  }
  });
};

// Start the server
startServer().catch(error => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
