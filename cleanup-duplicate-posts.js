#!/usr/bin/env node

/**
 * Database cleanup script to merge duplicate posts
 * This script identifies and merges posts that were split into separate entries
 * (one for text content, one for image) and combines them into single posts.
 */

import { getAll, getOne, run } from './server/db-operations.js';

const MERGE_TIME_THRESHOLD = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Find potential duplicate posts that should be merged
 */
async function findDuplicatePosts() {
  console.log('🔍 Scanning for duplicate posts...');
  
  // Get all posts ordered by user and creation time
  const allPosts = await getAll(`
    SELECT id, user_id, content, caption, image_url, created_at, updated_at
    FROM posts 
    ORDER BY user_id, created_at
  `);

  const duplicateGroups = [];
  const processedPosts = new Set();

  for (let i = 0; i < allPosts.length; i++) {
    const post = allPosts[i];
    
    if (processedPosts.has(post.id)) continue;

    // Look for potential duplicates within the time threshold
    const potentialDuplicates = [];
    const postTime = new Date(post.created_at).getTime();

    for (let j = i + 1; j < allPosts.length; j++) {
      const otherPost = allPosts[j];
      
      // Stop if we've moved to a different user
      if (otherPost.user_id !== post.user_id) break;
      
      const otherTime = new Date(otherPost.created_at).getTime();
      
      // Stop if we're beyond the time threshold
      if (otherTime - postTime > MERGE_TIME_THRESHOLD) break;
      
      if (processedPosts.has(otherPost.id)) continue;

      // Check if these posts should be merged
      if (shouldMergePosts(post, otherPost)) {
        potentialDuplicates.push(otherPost);
        processedPosts.add(otherPost.id);
      }
    }

    if (potentialDuplicates.length > 0) {
      duplicateGroups.push({
        primary: post,
        duplicates: potentialDuplicates
      });
      processedPosts.add(post.id);
    }
  }

  return duplicateGroups;
}

/**
 * Determine if two posts should be merged
 */
function shouldMergePosts(post1, post2) {
  // One has image, other doesn't
  const post1HasImage = !!post1.image_url;
  const post2HasImage = !!post2.image_url;
  
  if (post1HasImage === post2HasImage) return false;

  // One has caption, other doesn't (or both have captions)
  const post1HasCaption = !!post1.caption && post1.caption.trim().length > 0;
  const post2HasCaption = !!post2.caption && post2.caption.trim().length > 0;

  // Check if content is similar (same platform, topic, etc.)
  try {
    const content1 = JSON.parse(post1.content);
    const content2 = JSON.parse(post2.content);
    
    return content1.platform === content2.platform && 
           content1.topic === content2.topic;
  } catch (error) {
    // If we can't parse content, fall back to basic checks
    return true;
  }
}

/**
 * Merge a group of duplicate posts
 */
async function mergePosts(group) {
  const { primary, duplicates } = group;
  
  console.log(`📝 Merging ${duplicates.length + 1} posts for user ${primary.user_id}`);
  
  // Determine which post has the image and which has the best caption
  let finalImageUrl = primary.image_url;
  let finalCaption = primary.caption;
  let finalContent = primary.content;
  let finalUpdatedAt = primary.updated_at;
  let bestCaptionPart = '';
  let bestHashtagsPart = '';

  // Extract caption parts from primary post
  try {
    const primaryContent = JSON.parse(primary.content);
    bestCaptionPart = primaryContent.captionPart || '';
    bestHashtagsPart = primaryContent.hashtagsPart || '';
  } catch (error) {
    // If JSON parsing fails, try to extract from caption field
    if (primary.caption) {
      const parts = primary.caption.split('\n\n');
      bestCaptionPart = parts[0] || '';
      bestHashtagsPart = parts[1] || '';
    }
  }

  // Check duplicates for better content
  for (const duplicate of duplicates) {
    if (!finalImageUrl && duplicate.image_url) {
      finalImageUrl = duplicate.image_url;
    }

    // Use the most recent updated_at
    if (new Date(duplicate.updated_at) > new Date(finalUpdatedAt)) {
      finalUpdatedAt = duplicate.updated_at;
    }

    // Extract caption parts from duplicate
    try {
      const duplicateContent = JSON.parse(duplicate.content);

      // Use duplicate's caption parts if they're better (longer/more complete)
      if (duplicateContent.captionPart && duplicateContent.captionPart.length > bestCaptionPart.length) {
        bestCaptionPart = duplicateContent.captionPart;
        finalContent = duplicate.content; // Use the content with better caption
      }

      if (duplicateContent.hashtagsPart && duplicateContent.hashtagsPart.length > bestHashtagsPart.length) {
        bestHashtagsPart = duplicateContent.hashtagsPart;
        if (!finalContent || finalContent === primary.content) {
          finalContent = duplicate.content; // Use the content with better hashtags
        }
      }
    } catch (error) {
      // If JSON parsing fails, try to extract from caption field
      if (duplicate.caption && (!finalCaption || duplicate.caption.length > finalCaption.length)) {
        finalCaption = duplicate.caption;
        const parts = duplicate.caption.split('\n\n');
        if (parts[0] && parts[0].length > bestCaptionPart.length) {
          bestCaptionPart = parts[0];
        }
        if (parts[1] && parts[1].length > bestHashtagsPart.length) {
          bestHashtagsPart = parts[1];
        }
      }
    }
  }

  // Construct final caption from best parts
  if (bestCaptionPart || bestHashtagsPart) {
    if (bestCaptionPart && bestHashtagsPart) {
      finalCaption = `${bestCaptionPart}\n\n${bestHashtagsPart}`;
    } else if (bestCaptionPart) {
      finalCaption = bestCaptionPart;
    } else if (bestHashtagsPart) {
      finalCaption = bestHashtagsPart;
    }
  }

  // Update the content JSON to include the best caption parts
  try {
    const contentObj = JSON.parse(finalContent);
    contentObj.captionPart = bestCaptionPart;
    contentObj.hashtagsPart = bestHashtagsPart;
    finalContent = JSON.stringify(contentObj);
  } catch (error) {
    // If we can't parse/update the content, that's okay
  }

  // Update the primary post with merged data
  await run(`
    UPDATE posts 
    SET content = ?, caption = ?, image_url = ?, updated_at = ?
    WHERE id = ?
  `, [finalContent, finalCaption, finalImageUrl, finalUpdatedAt, primary.id]);

  // Delete the duplicate posts
  for (const duplicate of duplicates) {
    await run('DELETE FROM posts WHERE id = ?', [duplicate.id]);
    console.log(`   ❌ Deleted duplicate post ${duplicate.id}`);
  }

  console.log(`   ✅ Updated primary post ${primary.id} with merged content`);
}

/**
 * Main cleanup function
 */
async function cleanupDuplicatePosts(dryRun = false) {
  try {
    console.log(`🚀 Starting duplicate posts cleanup${dryRun ? ' (DRY RUN)' : ''}...\n`);

    const duplicateGroups = await findDuplicatePosts();

    if (duplicateGroups.length === 0) {
      console.log('✨ No duplicate posts found! Database is clean.');
      return;
    }

    console.log(`📊 Found ${duplicateGroups.length} groups of duplicate posts\n`);

    // Show preview of what will be merged
    console.log('📋 Preview of merges:');
    duplicateGroups.forEach((group, index) => {
      console.log(`${index + 1}. User ${group.primary.user_id}: ${group.duplicates.length + 1} posts`);
      console.log(`   Primary: ${group.primary.id} (${group.primary.image_url ? 'has image' : 'no image'}, ${group.primary.caption ? 'has caption' : 'no caption'})`);
      group.duplicates.forEach(dup => {
        console.log(`   Duplicate: ${dup.id} (${dup.image_url ? 'has image' : 'no image'}, ${dup.caption ? 'has caption' : 'no caption'})`);
      });
      console.log('');
    });

    if (dryRun) {
      console.log('🔍 DRY RUN: No changes were made to the database.');
      console.log(`   Would merge ${duplicateGroups.length} groups of duplicate posts.`);
      return;
    }

    // Perform the merges
    console.log('🔄 Starting merge process...\n');

    for (const group of duplicateGroups) {
      await mergePosts(group);
    }

    console.log(`\n✅ Cleanup completed! Merged ${duplicateGroups.length} groups of duplicate posts.`);

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  }
}

// Run the cleanup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const dryRun = process.argv.includes('--dry-run');

  if (dryRun) {
    console.log('🔍 Running in DRY RUN mode - no changes will be made\n');
  }

  cleanupDuplicatePosts(dryRun)
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}

export { cleanupDuplicatePosts, findDuplicatePosts };
