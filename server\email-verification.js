import { v4 as uuidv4 } from 'uuid';
import { hashPassword } from './auth.js';
import { generateVerificationCode, sendVerificationEmail, cleanupExpiredCodes } from './email-service.js';
import { getOne, getAll, getCurrentTimestamp, transaction } from './db-operations.js';
import { db } from './sqlite-db.js';
import { pool } from './db.js';

// Create a pending email verification
export const createEmailVerification = async (email, password, fullName) => {
  try {
    // Check if user already exists
    const existingUser = getOne('SELECT * FROM users WHERE email = ?', [email]);
    if (existingUser) {
      return { error: { message: 'User already exists' } };
    }

    // Clean up any existing verification for this email
    if (process.env.DATABASE_TYPE === 'mysql') {
      // MySQL cleanup
      await pool.query('DELETE FROM email_verifications WHERE email = ?', [email]);
    } else {
      // SQLite cleanup
      db.prepare('DELETE FROM email_verifications WHERE email = ?').run(email);
    }

    // Generate verification code and hash password
    const verificationCode = generateVerificationCode();
    const hashedPassword = hashPassword(password);
    const verificationId = uuidv4();
    const timestamp = getCurrentTimestamp();
    
    // Set expiration time (15 minutes from now)
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000).toISOString();

    // Store verification data
    if (process.env.DATABASE_TYPE === 'mysql') {
      // MySQL storage
      await pool.query(`
        INSERT INTO email_verifications (id, email, verification_code, full_name, password_hash, created_at, expires_at, attempts, verified)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [verificationId, email, verificationCode, fullName, hashedPassword, timestamp, expiresAt, 0, false]);
    } else {
      // SQLite storage
      db.prepare(`
        INSERT INTO email_verifications (id, email, verification_code, full_name, password_hash, created_at, expires_at, attempts, verified)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(verificationId, email, verificationCode, fullName, hashedPassword, timestamp, expiresAt, 0, 0);
    }

    // Send verification email
    const emailResult = await sendVerificationEmail(email, verificationCode, fullName);
    
    if (!emailResult.success) {
      // Clean up the verification record if email failed
      if (process.env.DATABASE_TYPE === 'mysql') {
        await connection.query('DELETE FROM email_verifications WHERE id = ?', [verificationId]);
      } else {
        db.prepare('DELETE FROM email_verifications WHERE id = ?').run(verificationId);
      }
      return { error: { message: 'Failed to send verification email' } };
    }

    return {
      data: {
        message: 'Verification email sent successfully',
        email: email,
        verificationId: verificationId
      },
      error: null
    };
  } catch (error) {
    console.error('Error creating email verification:', error);
    return { error: { message: 'Failed to create verification' } };
  }
};

// Verify email code and create user account
export const verifyEmailAndCreateAccount = async (email, verificationCode) => {
  try {
    // Find verification record
    let verification;
    if (process.env.DATABASE_TYPE === 'mysql') {
      // MySQL query
      const [rows] = await connection.query(
        'SELECT * FROM email_verifications WHERE email = ? AND verification_code = ? AND verified = FALSE',
        [email, verificationCode]
      );
      verification = rows[0];
    } else {
      // SQLite query
      verification = getOne(
        'SELECT * FROM email_verifications WHERE email = ? AND verification_code = ? AND verified = 0',
        [email, verificationCode]
      );
    }

    if (!verification) {
      return { error: { message: 'Invalid verification code' } };
    }

    // Check if code has expired
    const now = new Date();
    const expiresAt = new Date(verification.expires_at);
    if (now > expiresAt) {
      // Clean up expired code
      if (process.env.DATABASE_TYPE === 'mysql') {
        await connection.query('DELETE FROM email_verifications WHERE id = ?', [verification.id]);
      } else {
        db.prepare('DELETE FROM email_verifications WHERE id = ?').run(verification.id);
      }
      return { error: { message: 'Verification code has expired' } };
    }

    // Check attempt limit (max 5 attempts)
    if (verification.attempts >= 5) {
      return { error: { message: 'Too many verification attempts. Please request a new code.' } };
    }

    // Increment attempt count
    if (process.env.DATABASE_TYPE === 'mysql') {
      await connection.query('UPDATE email_verifications SET attempts = attempts + 1 WHERE id = ?', [verification.id]);
    } else {
      db.prepare('UPDATE email_verifications SET attempts = attempts + 1 WHERE id = ?').run(verification.id);
    }

    // Check if user already exists (double-check)
    const existingUser = getOne('SELECT * FROM users WHERE email = ?', [email]);
    if (existingUser) {
      // Clean up verification
      if (process.env.DATABASE_TYPE === 'mysql') {
        await connection.query('DELETE FROM email_verifications WHERE id = ?', [verification.id]);
      } else {
        db.prepare('DELETE FROM email_verifications WHERE id = ?').run(verification.id);
      }
      return { error: { message: 'User already exists' } };
    }

    // Create user account
    const userId = uuidv4();
    const timestamp = getCurrentTimestamp();

    try {
      if (process.env.DATABASE_TYPE === 'mysql') {
        // MySQL transaction
        await connection.beginTransaction();

        try {
          // Create profile
          await connection.query(`
            INSERT INTO profiles (id, full_name, plan, posts_count, posts_limit, last_post_time, created_at, updated_at, is_admin)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [userId, verification.full_name, 'free', 0, 3, null, timestamp, timestamp, false]);

          // Create user
          await connection.query(`
            INSERT INTO users (id, email, password_hash, created_at)
            VALUES (?, ?, ?, ?)
          `, [userId, verification.email, verification.password_hash, timestamp]);

          // Mark verification as completed
          await connection.query(`
            UPDATE email_verifications SET verified = TRUE WHERE id = ?
          `, [verification.id]);

          await connection.commit();
        } catch (error) {
          await connection.rollback();
          throw error;
        }
      } else {
        // SQLite transaction
        const createProfile = db.prepare(`
          INSERT INTO profiles (id, full_name, plan, posts_count, posts_limit, last_post_time, created_at, updated_at, is_admin)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const createUser = db.prepare(`
          INSERT INTO users (id, email, password_hash, created_at)
          VALUES (?, ?, ?, ?)
        `);

        const markVerified = db.prepare(`
          UPDATE email_verifications SET verified = 1 WHERE id = ?
        `);

        // Execute transaction
        const transaction = db.transaction(() => {
          createProfile.run(userId, verification.full_name, 'free', 0, 3, null, timestamp, timestamp, 0);
          createUser.run(userId, verification.email, verification.password_hash, timestamp);
          markVerified.run(verification.id);
        });

        transaction();
      }

      // Generate token (import this from auth.js)
      const { generateToken } = await import('./auth.js');
      const token = generateToken(userId);

      // Get the created profile
      const profile = getOne('SELECT * FROM profiles WHERE id = ?', [userId]);

      // Clean up verification record
      if (process.env.DATABASE_TYPE === 'mysql') {
        await connection.query('DELETE FROM email_verifications WHERE id = ?', [verification.id]);
      } else {
        db.prepare('DELETE FROM email_verifications WHERE id = ?').run(verification.id);
      }

      return {
        data: {
          user: {
            id: userId,
            email: verification.email,
            profile
          },
          token
        },
        error: null
      };
    } catch (dbError) {
      console.error('Database error during account creation:', dbError);
      return { error: { message: 'Failed to create account' } };
    }
  } catch (error) {
    console.error('Error verifying email:', error);
    return { error: { message: 'Verification failed' } };
  }
};

// Resend verification code
export const resendVerificationCode = async (email) => {
  try {
    // Find existing verification
    let verification;
    if (process.env.DATABASE_TYPE === 'mysql') {
      const [rows] = await connection.query(
        'SELECT * FROM email_verifications WHERE email = ? AND verified = FALSE',
        [email]
      );
      verification = rows[0];
    } else {
      verification = getOne(
        'SELECT * FROM email_verifications WHERE email = ? AND verified = 0',
        [email]
      );
    }

    if (!verification) {
      return { error: { message: 'No pending verification found for this email' } };
    }

    // Check if we can resend (limit to once every 2 minutes)
    const now = new Date();
    const createdAt = new Date(verification.created_at);
    const timeDiff = (now - createdAt) / 1000; // seconds

    if (timeDiff < 120) { // 2 minutes
      const waitTime = Math.ceil(120 - timeDiff);
      return { error: { message: `Please wait ${waitTime} seconds before requesting a new code` } };
    }

    // Generate new code and update expiration
    const newCode = generateVerificationCode();
    const newExpiresAt = new Date(Date.now() + 15 * 60 * 1000).toISOString();

    if (process.env.DATABASE_TYPE === 'mysql') {
      await connection.query(`
        UPDATE email_verifications
        SET verification_code = ?, expires_at = ?, attempts = 0, created_at = ?
        WHERE id = ?
      `, [newCode, newExpiresAt, getCurrentTimestamp(), verification.id]);
    } else {
      db.prepare(`
        UPDATE email_verifications
        SET verification_code = ?, expires_at = ?, attempts = 0, created_at = ?
        WHERE id = ?
      `).run(newCode, newExpiresAt, getCurrentTimestamp(), verification.id);
    }

    // Send new verification email
    const emailResult = await sendVerificationEmail(email, newCode, verification.full_name);
    
    if (!emailResult.success) {
      return { error: { message: 'Failed to send verification email' } };
    }

    return {
      data: {
        message: 'New verification code sent successfully',
        email: email
      },
      error: null
    };
  } catch (error) {
    console.error('Error resending verification code:', error);
    return { error: { message: 'Failed to resend verification code' } };
  }
};

// Cleanup expired codes (call this periodically)
export const cleanupExpiredVerifications = async () => {
  try {
    await cleanupExpiredCodes(process.env.DATABASE_TYPE === 'mysql' ? connection : db);
  } catch (error) {
    console.error('Error during cleanup:', error);
  }
};
