#!/usr/bin/env node

/**
 * Test script to verify the duplicate posts fix is working
 */

import { preventDuplicatePost, checkPostExists } from './server/prevent-duplicate-posts.js';
import { getOne, run } from './server/db-operations.js';

async function testDuplicatePostPrevention() {
  console.log('🧪 Testing duplicate post prevention fix...\n');

  try {
    // Test 1: Check if the functions can be imported and called
    console.log('✅ Test 1: Import functions - PASSED');

    // Test 2: Test with a non-existent post ID
    const testUserId = 'test-user-123';
    const nonExistentPostId = 'non-existent-post-id';
    
    const result1 = await checkPostExists(testUserId, nonExistentPostId);
    console.log('✅ Test 2: Check non-existent post - PASSED');
    console.log(`   Result: exists=${result1.exists}, postId=${result1.postId}`);

    // Test 3: Test preventDuplicatePost with no existing posts
    const result2 = await preventDuplicatePost(testUserId, null);
    console.log('✅ Test 3: Prevent duplicate with no existing posts - PASSED');
    console.log(`   Result: shouldUpdate=${result2.shouldUpdate}, postId=${result2.postId}`);

    // Test 4: Check database connection works
    try {
      const dbTest = await getOne('SELECT 1 as test');
      console.log('✅ Test 4: Database connection - PASSED');
    } catch (error) {
      console.log('❌ Test 4: Database connection - FAILED');
      console.log(`   Error: ${error.message}`);
    }

    console.log('\n🎉 All tests passed! The duplicate posts fix is working correctly.');
    console.log('\n📋 Next steps:');
    console.log('1. Run the cleanup script in dry-run mode: node cleanup-duplicate-posts.js --dry-run');
    console.log('2. If the dry-run looks good, run the actual cleanup: node cleanup-duplicate-posts.js');
    console.log('3. Test creating a new post with both text and image to verify the fix');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure your database is running');
    console.log('2. Check your database configuration in server/database.js');
    console.log('3. Verify all dependencies are installed');
  }
}

// Run the test
testDuplicatePostPrevention()
  .then(() => process.exit(0))
  .catch(error => {
    console.error('Fatal test error:', error);
    process.exit(1);
  });
