# Duplicate Posts Fix

## Problem
Your production MySQL database was creating separate posts for image and text content instead of combining them into a single post. This happened because the duplicate prevention logic was hardcoded to use SQLite and didn't work with MySQL.

## What Was Fixed

### 1. Updated `server/prevent-duplicate-posts.js`
- **Before**: Hardcoded to use SQLite database operations
- **After**: Uses unified database operations that work with both SQLite (local) and MySQL (production)

This ensures that when an image is generated for an existing text post, it updates the same post instead of creating a new one.

### 2. Fixed Caption Overwriting Issue in `/api/generate-image` endpoint
- **Root Problem**: When an image was generated for an existing post, the server was overwriting the good caption data with placeholder "Generating..." values
- **Solution**: Modified the image generation endpoint to preserve existing caption data when updating a post with an image
- **Key Changes**:
  - Retrieves existing post data before updating
  - Preserves existing `captionPart` and `hashtagsPart` if they're not "Generating..."
  - Merges content JSON intelligently to keep the best data
  - Reconstructs the caption field from preserved parts

### 3. Fixed Caption Display Issue in `server/index.js`
- **Problem**: Gallery wasn't showing captions because they were stored in the `content` JSON field but the `caption` database field was null
- **Solution**: Updated all post retrieval endpoints to construct captions from `captionPart` and `hashtagsPart` in the content JSON when the caption field is null
- **Affected endpoints**:
  - `/api/posts` (main gallery)
  - `/api/posts/:postId` (individual post)
  - `/api/admin/users/:userId/gallery` (admin gallery)

### 4. Fixed Frontend "Generating..." Issue in `src/pages/Create.tsx`
- **Problem**: When visiting `/create/<id>` from gallery, the page showed "Generating..." instead of actual captions
- **Root Cause**: Frontend was reading placeholder "Generating..." values from content JSON instead of using the constructed caption from server
- **Solution**: Updated frontend logic to ignore "Generating..." placeholder values and use the actual caption data from the server
- **Key Changes**:
  - Added checks to ignore `captionPart: "Generating..."` and `hashtagsPart: "Generating..."`
  - Prioritizes server-constructed caption over content JSON placeholders
  - Falls back to splitting the caption field when content JSON has placeholders

### 5. Created Database Cleanup Script
The `cleanup-duplicate-posts.js` script identifies and merges existing duplicate posts in your database.

## How to Use the Cleanup Script

### Step 1: Dry Run (Recommended)
First, run the script in dry-run mode to see what would be changed without making any actual changes:

```bash
node cleanup-duplicate-posts.js --dry-run
```

This will show you:
- How many duplicate groups were found
- Which posts would be merged
- Preview of the changes without modifying the database

### Step 2: Run the Actual Cleanup
If the dry run looks correct, run the actual cleanup:

```bash
node cleanup-duplicate-posts.js
```

This will:
- Merge duplicate posts by combining their content, captions, and images
- Delete the duplicate entries
- Keep the primary post with all merged data

## What the Script Does

### Detection Logic
The script identifies duplicate posts by looking for:
1. **Same user**: Posts belonging to the same user
2. **Similar timing**: Created within 5 minutes of each other
3. **Complementary content**: One has an image, the other doesn't
4. **Similar metadata**: Same platform and topic (when available)

### Merging Logic
When merging posts, the script:
- Combines the best caption from both posts
- Uses the image URL from whichever post has it
- Preserves the most complete content JSON
- Uses the most recent updated timestamp
- Deletes the duplicate posts after merging

## Example Output

```
🚀 Starting duplicate posts cleanup...

📊 Found 3 groups of duplicate posts

📋 Preview of merges:
1. User c783dcb-9a88-44de-8901: 2 posts
   Primary: 6527f1c-af1-4913-a7e8-d67b27f98ca3 (no image, has caption)
   Duplicate: 6527f1c-af1-4913-a7e8-d67b27f98ca4 (has image, no caption)

🔄 Starting merge process...

📝 Merging 2 posts for user c783dcb-9a88-44de-8901
   ❌ Deleted duplicate post 6527f1c-af1-4913-a7e8-d67b27f98ca4
   ✅ Updated primary post 6527f1c-af1-4913-a7e8-d67b27f98ca3 with merged content

✅ Cleanup completed! Merged 3 groups of duplicate posts.
```

## Safety Features

- **Dry run mode**: Test the script without making changes
- **Detailed logging**: See exactly what will be changed
- **Error handling**: Script stops if any errors occur
- **Backup recommendation**: Always backup your database before running

## Backup Recommendation

Before running the cleanup script on your production database, create a backup:

```sql
-- For MySQL
mysqldump -u username -p database_name > backup_before_cleanup.sql
```

## Testing

After running the cleanup:
1. Check your gallery to ensure posts display correctly
2. Create a new post with both text and image to verify the fix works
3. Verify that no new duplicate posts are created

## Future Prevention

The fix to `prevent-duplicate-posts.js` ensures this issue won't happen again. The system will now properly:
1. Update existing posts when adding images
2. Work correctly with both SQLite (local) and MySQL (production)
3. Prevent creation of duplicate posts for the same content
