// Client-side authentication utilities

// Store token in localStorage
const storeToken = (token) => {
  localStorage.setItem('auth_token', token);
};

// Get token from localStorage
const getToken = () => {
  return localStorage.getItem('auth_token');
};

// Remove token from localStorage
const removeToken = () => {
  localStorage.removeItem('auth_token');
};

// Store user data in localStorage
const storeUser = (user) => {
  localStorage.setItem('user', JSON.stringify(user));
};

// Get user data from localStorage
const getUser = () => {
  const userData = localStorage.getItem('user');
  return userData ? JSON.parse(userData) : null;
};

// Remove user data from localStorage
const removeUser = () => {
  localStorage.removeItem('user');
};

// Register a new user (now sends verification email)
const registerUser = async (email, password, fullName) => {
  try {
    const response = await fetch('/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password, fullName }),
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle specific error cases
      if (response.status === 429) {
        // Rate limiting error - IP limit or too many requests
        return {
          error: {
            message: data.error || data.message || 'Account limit reached. Only one account is allowed per IP address for security reasons.',
            isRateLimit: true,
            statusCode: 429
          }
        };
      }

      return { error: { message: data.error || 'Registration failed' } };
    }

    // Registration now returns verification info instead of user data
    return { data, error: null };
  } catch (error) {
    console.error('Registration error:', error);
    return { error: { message: 'Registration failed. Please check your connection and try again.' } };
  }
};

// Verify email and complete registration
const verifyEmail = async (email, verificationCode) => {
  try {
    const response = await fetch('/api/auth/verify-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, verificationCode }),
    });

    const data = await response.json();

    if (!response.ok) {
      return { error: { message: data.error || 'Verification failed' } };
    }

    // Store token and user data after successful verification
    storeToken(data.token);
    storeUser(data.user);

    return { data, error: null };
  } catch (error) {
    console.error('Verification error:', error);
    return { error: { message: 'Verification failed' } };
  }
};

// Resend verification code
const resendVerificationCode = async (email) => {
  try {
    const response = await fetch('/api/auth/resend-verification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();

    if (!response.ok) {
      return { error: { message: data.error || 'Failed to resend code' } };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Resend error:', error);
    return { error: { message: 'Failed to resend verification code' } };
  }
};

// Login a user
const loginUser = async (email, password) => {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();

    if (!response.ok) {
      return { error: { message: data.error || 'Login failed' } };
    }

    // Store token and user data
    storeToken(data.token);
    storeUser(data.user);

    return { data, error: null };
  } catch (error) {
    console.error('Login error:', error);
    return { error: { message: 'Login failed' } };
  }
};

// Logout a user
const logoutUser = () => {
  removeToken();
  removeUser();
  return { error: null };
};

// Get user profile
const getUserProfile = async () => {
  try {
    const token = getToken();

    if (!token) {
      return { error: { message: 'Not authenticated' } };
    }

    const response = await fetch('/api/profile', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return { error: { message: data.error || 'Failed to get profile' } };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Get profile error:', error);
    return { error: { message: 'Failed to get profile' } };
  }
};

// Update user profile
const updateUserProfile = async (updates) => {
  try {
    const token = getToken();

    if (!token) {
      return { error: { message: 'Not authenticated' } };
    }

    const response = await fetch('/api/profile', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(updates),
    });

    const data = await response.json();

    if (!response.ok) {
      return { error: { message: data.error || 'Failed to update profile' } };
    }

    // Update stored user data
    const user = getUser();
    if (user && user.profile) {
      user.profile = { ...user.profile, ...updates };
      storeUser(user);
    }

    return { data, error: null };
  } catch (error) {
    console.error('Update profile error:', error);
    return { error: { message: 'Failed to update profile' } };
  }
};

// Force refresh user profile from the server
const refreshUserProfile = async () => {
  try {
    const token = getToken();

    if (!token) {
      return { error: { message: 'Not authenticated' } };
    }

    const response = await fetch('/api/profile/refresh', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return { error: { message: data.error || 'Failed to refresh profile' } };
    }

    // Update stored user data
    const user = getUser();
    if (user) {
      user.profile = data;
      storeUser(user);
    }

    return { data, error: null };
  } catch (error) {
    console.error('Refresh profile error:', error);
    return { error: { message: 'Failed to refresh profile' } };
  }
};

export {
  registerUser,
  verifyEmail,
  resendVerificationCode,
  loginUser,
  logoutUser,
  getUserProfile,
  updateUserProfile,
  refreshUserProfile,
  getToken,
  getUser
};
