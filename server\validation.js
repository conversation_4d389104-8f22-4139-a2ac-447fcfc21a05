import { z } from 'zod';

// User validation schemas
export const registerSchema = z.object({
  email: z.string().email('Invalid email format').max(255),
  password: z.string().min(8, 'Password must be at least 8 characters').max(128),
  fullName: z.string().min(1, 'Full name is required').max(100).trim()
});

export const loginSchema = z.object({
  email: z.string().email('Invalid email format').max(255),
  password: z.string().min(1, 'Password is required').max(128)
});

export const verifyEmailSchema = z.object({
  email: z.string().email('Invalid email format').max(255),
  verificationCode: z.string().length(6, 'Verification code must be 6 digits').regex(/^\d{6}$/, 'Verification code must contain only numbers')
});

export const resendVerificationSchema = z.object({
  email: z.string().email('Invalid email format').max(255)
});

// Post generation validation
export const generatePostSchema = z.object({
  topic: z.string().min(1, 'Topic is required').max(500).trim(),
  platform: z.enum(['Instagram', 'X', 'LinkedIn', 'Facebook', 'Twitter'], {
    errorMap: () => ({ message: 'Invalid platform. Must be one of: Instagram, X, LinkedIn, Facebook, Twitter' })
  }),
  tone: z.enum(['Professional', 'Casual', 'Enthusiastic', 'Informative', 'Humorous', 'Inspirational', 'Formal', 'Friendly'], {
    errorMap: () => ({ message: 'Invalid tone. Must be one of: Professional, Casual, Enthusiastic, Informative, Humorous, Inspirational, Formal, Friendly' })
  }),
  hashtagCount: z.number().int().min(0).max(10).default(5),
  imageSize: z.enum(['Auto', '1:1', '4:5', '5:4', '3:2', '16:9']).optional().default('4:5'),
  quality: z.enum(['fast', 'balanced', 'quality']).optional().default('balanced'),
  postId: z.string().optional().nullable()
});

// Image generation validation
export const generateImageSchema = z.object({
  imagePrompt: z.string().min(1, 'Image prompt is required').max(1000).trim(),
  postContent: z.string().optional(),
  caption: z.string().optional(),
  userId: z.string().optional(),
  style: z.enum(['realistic', 'artistic', 'minimalist', 'vibrant']).optional()
});

// Profile update validation
export const updateProfileSchema = z.object({
  fullName: z.string().min(1, 'Full name is required').max(100).trim().optional(),
  bio: z.string().max(500).trim().optional(),
  website: z.string().url('Invalid URL format').optional().or(z.literal(''))
});

// Contact form validation
export const contactSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100).trim(),
  email: z.string().email('Invalid email format').max(255),
  message: z.string().min(1, 'Message is required').max(1000).trim()
});

// Admin validation
export const updateUserPlanSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  plan: z.enum(['free', 'basic', 'pro', 'ultra']),
  forceLogout: z.boolean().default(false)
});

// General ID validation
export const uuidSchema = z.string().uuid('Invalid ID format');

// Payment validation
export const paymentSchema = z.object({
  paymentId: z.string().min(1, 'Payment ID is required'),
  payerId: z.string().min(1, 'Payer ID is required')
});

// Validation middleware factory
export const validateBody = (schema) => {
  return (req, res, next) => {
    try {
      const validated = schema.parse(req.body);
      req.body = validated;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: 'Validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
      }
      next(error);
    }
  };
};

// Validation for params
export const validateParams = (schema) => {
  return (req, res, next) => {
    try {
      const validated = schema.parse(req.params);
      req.params = validated;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: 'Invalid parameters',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
      }
      next(error);
    }
  };
};

// Sanitize string inputs (basic XSS prevention)
export const sanitizeString = (str) => {
  if (typeof str !== 'string') return str;
  return str
    .replace(/[<>]/g, '') // Remove basic HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

// Sanitize object recursively
export const sanitizeObject = (obj) => {
  if (typeof obj !== 'object' || obj === null) {
    return typeof obj === 'string' ? sanitizeString(obj) : obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  const sanitized = {};
  for (const [key, value] of Object.entries(obj)) {
    sanitized[key] = sanitizeObject(value);
  }
  return sanitized;
};