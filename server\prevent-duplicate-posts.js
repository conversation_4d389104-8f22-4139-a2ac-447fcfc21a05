// This file contains modifications to prevent duplicate posts

import { getOne } from './db-operations.js';

/**
 * Function to check if a post exists and return its ID
 * @param {string} userId - The user ID
 * @param {string} postId - The post ID to check
 * @returns {Promise<{exists: boolean, postId: string|null}>}
 */
export const checkPostExists = async (userId, postId) => {
  try {
    if (!postId) return { exists: false, postId: null };

    // Use unified database operations that work with both SQLite and MySQL
    const post = await getOne('SELECT * FROM posts WHERE id = ? AND user_id = ?', [postId, userId]);

    return {
      exists: !!post,
      postId: post ? post.id : null
    };
  } catch (error) {
    console.error('Error checking if post exists:', error);
    return { exists: false, postId: null };
  }
};

/**
 * Modify the savePost function to prioritize updating existing posts
 * This will help prevent creating duplicate posts
 */
export const preventDuplicatePost = async (userId, postId) => {
  try {
    // First check if the post exists
    const { exists, postId: existingPostId } = await checkPostExists(userId, postId);

    if (exists) {
      console.log(`Post ${existingPostId} exists for user ${userId}, will update it`);
      return { shouldUpdate: true, postId: existingPostId };
    }

    // If the post doesn't exist, check for the most recent post
    const recentPost = await getOne(
      'SELECT * FROM posts WHERE user_id = ? ORDER BY created_at DESC LIMIT 1',
      [userId]
    );

    // If there's a recent post with no image, we'll update that instead
    if (recentPost && !recentPost.image_url) {
      console.log(`Found recent post ${recentPost.id} with no image, will update it`);
      return { shouldUpdate: true, postId: recentPost.id };
    }

    // Otherwise, we'll create a new post
    return { shouldUpdate: false, postId: null };
  } catch (error) {
    console.error('Error in preventDuplicatePost:', error);
    return { shouldUpdate: false, postId: null };
  }
};