import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Load environment variables
dotenv.config();

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database type from environment variable
const DATABASE_TYPE = process.env.DATABASE_TYPE || 'sqlite';

let db = null;
let pool = null;

// Initialize the appropriate database based on DATABASE_TYPE
const initializeDatabase = async () => {
  if (DATABASE_TYPE === 'mysql') {
    console.log('Initializing MySQL database...');
    const mysql = await import('mysql2/promise');
    
    // Create a connection pool
    pool = mysql.createPool({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'socialspark',
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      enableKeepAlive: true,
      keepAliveInitialDelay: 0,
    });

    // Test the connection
    try {
      const connection = await pool.getConnection();
      console.log('MySQL connection established successfully');
      connection.release();
    } catch (error) {
      console.error('Error connecting to MySQL:', error);
      throw error;
    }

    // Initialize MySQL database and tables
    await initMySQLDatabase();
    
  } else {
    console.log('Initializing SQLite database...');
    const Database = (await import('better-sqlite3')).default;
    
    // Create data directory if it doesn't exist
    const dataDir = path.join(__dirname, '..', 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Database file path
    const dbPath = path.join(dataDir, 'socialspark.db');
    
    // Create and initialize the database
    db = new Database(dbPath);
    
    // Enable foreign keys
    db.pragma('foreign_keys = ON');
    
    console.log('SQLite connection established successfully');
    
    // Initialize SQLite database and tables
    initSQLiteDatabase();
  }
};

// Initialize MySQL database and tables
const initMySQLDatabase = async () => {
  try {
    // First, try to connect to MySQL without specifying a database
    const mysql = await import('mysql2/promise');
    const tempPool = mysql.createPool({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      waitForConnections: true,
      connectionLimit: 1
    });

    // Create the database if it doesn't exist
    const tempConnection = await tempPool.getConnection();
    await tempConnection.query(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME || 'socialspark'}`);
    tempConnection.release();
    await tempPool.end();

    console.log(`Ensured database ${process.env.DB_NAME || 'socialspark'} exists`);

    // Now connect to the specific database and create tables
    const connection = await pool.getConnection();

    // Create profiles table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS profiles (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        full_name VARCHAR(255),
        plan ENUM('free', 'basic', 'pro', 'ultra') DEFAULT 'free',
        posts_count INT DEFAULT 0,
        posts_limit INT DEFAULT 3,
        last_post_time TIMESTAMP NULL,
        subscription_status ENUM('active', 'cancelled', 'expired') DEFAULT 'active',
        billing_cycle ENUM('monthly', 'yearly') NULL,
        next_billing_date TIMESTAMP NULL,
        paypal_order_id VARCHAR(255) NULL,
        paypal_subscription_id VARCHAR(255) NULL,
        is_admin BOOLEAN DEFAULT FALSE
      )
    `);

    // Create posts table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS posts (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        user_id VARCHAR(36) NOT NULL,
        content TEXT NOT NULL,
        caption TEXT,
        image_url TEXT,
        published BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
      )
    `);

    // Create users table for basic authentication
    await connection.query(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY,
        email VARCHAR(255) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (id) REFERENCES profiles(id) ON DELETE CASCADE
      )
    `);

    // Create email_verifications table for email verification codes
    await connection.query(`
      CREATE TABLE IF NOT EXISTS email_verifications (
        id VARCHAR(36) PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        verification_code VARCHAR(6) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        attempts INTEGER DEFAULT 0,
        verified BOOLEAN DEFAULT FALSE,
        INDEX idx_email (email),
        INDEX idx_code (verification_code),
        INDEX idx_expires (expires_at)
      )
    `);

    // Create analytics tables
    await connection.query(`
      CREATE TABLE IF NOT EXISTS analytics_events (
        id VARCHAR(36) PRIMARY KEY,
        event_type VARCHAR(100) NOT NULL,
        user_id VARCHAR(36),
        session_id VARCHAR(36),
        ip_address VARCHAR(45),
        user_agent TEXT,
        event_data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_event_type (event_type),
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at)
      )
    `);

    await connection.query(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        session_token VARCHAR(255) NOT NULL,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_session (user_id, session_token),
        INDEX idx_user_id (user_id),
        INDEX idx_session_token (session_token),
        INDEX idx_last_activity (last_activity)
      )
    `);

    // Create indexes for better performance
    // Check if index exists before creating it (MySQL compatible approach)
    try {
      const [indexExists] = await connection.query(`
        SELECT COUNT(*) as count
        FROM information_schema.statistics
        WHERE table_schema = DATABASE()
        AND table_name = 'posts'
        AND index_name = 'idx_posts_user_id'
      `);

      if (indexExists[0].count === 0) {
        await connection.query('CREATE INDEX idx_posts_user_id ON posts(user_id)');
        console.log('Created index idx_posts_user_id');
      } else {
        console.log('Index idx_posts_user_id already exists');
      }
    } catch (err) {
      console.log('Note: Could not create posts index:', err.message);
    }

    try {
      const [indexExists] = await connection.query(`
        SELECT COUNT(*) as count
        FROM information_schema.statistics
        WHERE table_schema = DATABASE()
        AND table_name = 'users'
        AND index_name = 'idx_users_email'
      `);

      if (indexExists[0].count === 0) {
        await connection.query('CREATE INDEX idx_users_email ON users(email)');
        console.log('Created index idx_users_email');
      } else {
        console.log('Index idx_users_email already exists');
      }
    } catch (err) {
      console.log('Note: Could not create users index:', err.message);
    }

    connection.release();
    console.log('MySQL database and tables initialized successfully');
  } catch (error) {
    console.error('Error initializing MySQL database:', error);
    throw error;
  }
};

// Initialize SQLite database and tables
const initSQLiteDatabase = () => {
  try {
    // Create profiles table
    db.prepare(`
      CREATE TABLE IF NOT EXISTS profiles (
        id TEXT PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        full_name TEXT,
        plan TEXT DEFAULT 'free' CHECK(plan IN ('free', 'basic', 'pro', 'ultra')),
        posts_count INTEGER DEFAULT 0,
        posts_limit INTEGER DEFAULT 3,
        last_post_time TIMESTAMP,
        subscription_status TEXT DEFAULT 'active' CHECK(subscription_status IN ('active', 'cancelled', 'expired')),
        billing_cycle TEXT CHECK(billing_cycle IN ('monthly', 'yearly')),
        next_billing_date TIMESTAMP,
        paypal_order_id TEXT,
        paypal_subscription_id TEXT,
        is_admin INTEGER DEFAULT 0
      )
    `).run();

    // Create posts table
    db.prepare(`
      CREATE TABLE IF NOT EXISTS posts (
        id TEXT PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        user_id TEXT NOT NULL,
        content TEXT NOT NULL,
        caption TEXT,
        image_url TEXT,
        published INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
      )
    `).run();

    // Create users table for basic authentication
    db.prepare(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL UNIQUE,
        password_hash TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (id) REFERENCES profiles(id) ON DELETE CASCADE
      )
    `).run();

    // Create email_verifications table for email verification codes
    db.prepare(`
      CREATE TABLE IF NOT EXISTS email_verifications (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL,
        verification_code TEXT NOT NULL,
        full_name TEXT NOT NULL,
        password_hash TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        attempts INTEGER DEFAULT 0,
        verified INTEGER DEFAULT 0
      )
    `).run();

    // Create analytics tables
    db.prepare(`
      CREATE TABLE IF NOT EXISTS analytics_events (
        id TEXT PRIMARY KEY,
        event_type TEXT NOT NULL,
        user_id TEXT,
        session_id TEXT,
        ip_address TEXT,
        user_agent TEXT,
        event_data TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `).run();

    db.prepare(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        session_token TEXT NOT NULL,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address TEXT,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, session_token)
      )
    `).run();

    // Create indexes for better performance
    db.prepare('CREATE INDEX IF NOT EXISTS idx_posts_user_id ON posts(user_id)').run();
    db.prepare('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)').run();
    db.prepare('CREATE INDEX IF NOT EXISTS idx_analytics_events_type ON analytics_events(event_type)').run();
    db.prepare('CREATE INDEX IF NOT EXISTS idx_analytics_events_user ON analytics_events(user_id)').run();
    db.prepare('CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON user_sessions(user_id)').run();
    db.prepare('CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)').run();

    console.log('SQLite database and tables initialized successfully');
  } catch (error) {
    console.error('Error initializing SQLite database:', error);
    throw error;
  }
};

// Test database connection
const testConnection = async () => {
  try {
    if (DATABASE_TYPE === 'mysql') {
      if (!pool) {
        console.error('MySQL pool not initialized');
        return false;
      }
      const connection = await pool.getConnection();
      console.log('MySQL connection test successful');
      connection.release();
      return true;
    } else {
      if (!db) {
        console.error('SQLite database not initialized');
        return false;
      }
      // Execute a simple query to test the connection
      db.prepare('SELECT 1 AS test').get();
      console.log('SQLite connection test successful');
      return true;
    }
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
};

export { DATABASE_TYPE, db, pool, initializeDatabase, testConnection };
